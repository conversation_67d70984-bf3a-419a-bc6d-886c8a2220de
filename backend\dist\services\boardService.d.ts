import type { NewBoard, UpdateBoard, NewBoardColumn, UpdateBoardColumn, Board, BoardColumn } from '../db/schema';
export declare class BoardService {
    createBoard(boardData: Omit<NewBoard, 'id' | 'createdAt' | 'updatedAt'>, createdBy: string): Promise<Board>;
    getBoardById(boardId: string, includeColumns?: boolean): Promise<{
        id: string;
        organizationId: string;
        name: string;
        description: string | null;
        color: string | null;
        isTemplate: boolean;
        isArchived: boolean;
        settings: {
            allowComments?: boolean;
            allowAttachments?: boolean;
            cardCoverEnabled?: boolean;
            dueDateReminders?: boolean;
            emailNotifications?: boolean;
            visibility?: "private" | "organization" | "public";
        } | null;
        createdBy: string;
        createdAt: Date;
        updatedAt: Date;
    } | {
        columns: {
            id: string;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            color: string | null;
            boardId: string;
            position: number;
            isCollapsed: boolean;
            cardLimit: number | null;
        }[];
        id?: string;
        organizationId?: string;
        name?: string;
        description?: string | null;
        color?: string | null;
        isTemplate?: boolean;
        isArchived?: boolean;
        settings?: {
            allowComments?: boolean;
            allowAttachments?: boolean;
            cardCoverEnabled?: boolean;
            dueDateReminders?: boolean;
            emailNotifications?: boolean;
            visibility?: "private" | "organization" | "public";
        } | null;
        createdBy?: string;
        createdAt?: Date;
        updatedAt?: Date;
    } | undefined>;
    getOrganizationBoards(organizationId: string, includeArchived?: boolean): Promise<{
        id: string;
        organizationId: string;
        name: string;
        description: string | null;
        color: string | null;
        isTemplate: boolean;
        isArchived: boolean;
        settings: {
            allowComments?: boolean;
            allowAttachments?: boolean;
            cardCoverEnabled?: boolean;
            dueDateReminders?: boolean;
            emailNotifications?: boolean;
            visibility?: "private" | "organization" | "public";
        } | null;
        createdBy: string;
        createdAt: Date;
        updatedAt: Date;
    }[]>;
    updateBoard(boardId: string, updates: UpdateBoard, userId: string): Promise<Board>;
    deleteBoard(boardId: string, userId: string): Promise<void>;
    getBoardColumns(boardId: string): Promise<BoardColumn[]>;
    createBoardColumn(columnData: Omit<NewBoardColumn, 'id' | 'createdAt' | 'updatedAt'>): Promise<BoardColumn>;
    updateBoardColumn(columnId: string, updates: UpdateBoardColumn): Promise<BoardColumn>;
    deleteBoardColumn(columnId: string): Promise<void>;
    reorderColumns(boardId: string, columnIds: string[]): Promise<BoardColumn[]>;
    getBoardStats(boardId: string): Promise<{
        columns: number;
        cards: number;
    }>;
    canUserAccessBoard(boardId: string, userId: string): Promise<boolean>;
}
export declare const boardService: BoardService;
//# sourceMappingURL=boardService.d.ts.map