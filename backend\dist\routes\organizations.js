"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const organizationService_1 = require("../services/organizationService");
const rbacService_1 = require("../services/rbacService");
const schema_1 = require("../db/schema");
const router = (0, express_1.Router)();
router.post('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const validatedData = schema_1.insertOrganizationSchema.parse(req.body);
    const organization = await organizationService_1.organizationService.createOrganization(validatedData, req.user.id);
    res.status(201).json({
        success: true,
        data: {
            organization,
            message: 'Organization created successfully',
        },
    });
}));
router.get('/my-organizations', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const query = req.query.q;
    const organizations = await organizationService_1.organizationService.searchUserOrganizations(req.user.id, query);
    res.json({
        success: true,
        data: {
            organizations,
        },
    });
}));
router.post('/switch/:organizationId', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { organizationId } = req.params;
    const result = await organizationService_1.organizationService.switchOrganization(req.user.id, organizationId);
    req.session.organizationId = organizationId;
    res.json({
        success: true,
        data: {
            ...result,
            message: 'Organization switched successfully',
        },
    });
}));
router.use('/:organizationId', auth_1.validateOrganization);
router.get('/:organizationId', (0, auth_1.requirePermission)('org:read'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const stats = await organizationService_1.organizationService.getOrganizationStats(req.params.organizationId);
    res.json({
        success: true,
        data: {
            organization: req.organization,
            membership: req.membership,
            stats,
        },
    });
}));
router.patch('/:organizationId', (0, auth_1.requirePermission)('org:update'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const validatedData = schema_1.updateOrganizationSchema.parse(req.body);
    const updatedOrganization = await organizationService_1.organizationService.updateOrganization(req.params.organizationId, validatedData, req.user.id);
    res.json({
        success: true,
        data: {
            organization: updatedOrganization,
            message: 'Organization updated successfully',
        },
    });
}));
router.delete('/:organizationId', (0, auth_1.requireRole)('owner'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    await organizationService_1.organizationService.deleteOrganization(req.params.organizationId, req.user.id);
    res.json({
        success: true,
        data: {
            message: 'Organization deleted successfully',
        },
    });
}));
router.get('/:organizationId/members', (0, auth_1.requirePermission)('org:read'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const members = await organizationService_1.organizationService.getOrganizationMembers(req.params.organizationId);
    res.json({
        success: true,
        data: {
            members,
        },
    });
}));
router.patch('/:organizationId/members/:userId', (0, auth_1.requirePermission)('org:manage_members'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { organizationId, userId } = req.params;
    const validatedData = schema_1.updateOrganizationMemberSchema.parse(req.body);
    if (!validatedData.role) {
        throw (0, errorHandler_1.createError)('Role is required for member update', 400);
    }
    const updatedMembership = await rbacService_1.rbacService.updateMemberRole(organizationId, userId, validatedData.role, req.user.id);
    res.json({
        success: true,
        data: {
            membership: updatedMembership,
            message: 'Member role updated successfully',
        },
    });
}));
router.delete('/:organizationId/members/:userId', (0, auth_1.requirePermission)('org:manage_members'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { organizationId, userId } = req.params;
    await rbacService_1.rbacService.removeMember(organizationId, userId, req.user.id);
    res.json({
        success: true,
        data: {
            message: 'Member removed successfully',
        },
    });
}));
exports.default = router;
//# sourceMappingURL=organizations.js.map