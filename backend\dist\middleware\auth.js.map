{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AACA,iDAAsC;AACtC,yCAAyE;AACzE,6CAAsC;AACtC,iDAA6C;AA2BtC,MAAM,eAAe,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACvF,IAAI,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACxC,MAAM,IAAA,0BAAW,EAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;QACpD,CAAC;QAGD,MAAM,IAAI,GAAG,MAAM,eAAE;aAClB,MAAM,CAAC;YACN,EAAE,EAAE,cAAK,CAAC,EAAE;YACZ,KAAK,EAAE,cAAK,CAAC,KAAK;YAClB,SAAS,EAAE,cAAK,CAAC,SAAS;YAC1B,QAAQ,EAAE,cAAK,CAAC,QAAQ;YACxB,MAAM,EAAE,cAAK,CAAC,MAAM;SACrB,CAAC;aACD,IAAI,CAAC,cAAK,CAAC;aACX,KAAK,CAAC,IAAA,gBAAE,EAAC,cAAK,CAAC,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACvC,KAAK,CAAC,CAAC,CAAC,CAAC;QAEZ,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAEhD,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBAC1B,IAAI,GAAG;oBAAE,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;YACH,MAAM,IAAA,0BAAW,EAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC;QAC7D,CAAC;QAGD,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACnB,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAlCW,QAAA,eAAe,mBAkC1B;AAGK,MAAM,oBAAoB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC5F,IAAI,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,IAAA,0BAAW,EAAC,8BAA8B,EAAE,GAAG,CAAC,CAAC;QACzD,CAAC;QAGD,MAAM,cAAc,GAAG,GAAG,CAAC,MAAM,CAAC,cAAc,IAAI,GAAG,CAAC,IAAI,CAAC,cAAc,IAAI,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC;QAExG,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAA,0BAAW,EAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;QACxD,CAAC;QAGD,MAAM,UAAU,GAAG,MAAM,eAAE;aACxB,MAAM,CAAC;YACN,YAAY,EAAE;gBACZ,EAAE,EAAE,sBAAa,CAAC,EAAE;gBACpB,IAAI,EAAE,sBAAa,CAAC,IAAI;gBACxB,IAAI,EAAE,sBAAa,CAAC,IAAI;aACzB;YACD,IAAI,EAAE,4BAAmB,CAAC,IAAI;YAC9B,MAAM,EAAE,4BAAmB,CAAC,MAAM;SACnC,CAAC;aACD,IAAI,CAAC,4BAAmB,CAAC;aACzB,SAAS,CAAC,sBAAa,EAAE,IAAA,gBAAE,EAAC,sBAAa,CAAC,EAAE,EAAE,4BAAmB,CAAC,cAAc,CAAC,CAAC;aAClF,KAAK,CACJ,IAAA,iBAAG,EACD,IAAA,gBAAE,EAAC,4BAAmB,CAAC,cAAc,EAAE,cAAc,CAAC,EACtD,IAAA,gBAAE,EAAC,4BAAmB,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAC3C,IAAA,gBAAE,EAAC,4BAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CACzC,CACF;aACA,KAAK,CAAC,CAAC,CAAC,CAAC;QAEZ,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YACvB,MAAM,IAAA,0BAAW,EAAC,kDAAkD,EAAE,GAAG,CAAC,CAAC;QAC7E,CAAC;QAGD,GAAG,CAAC,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;QAC9C,GAAG,CAAC,UAAU,GAAG;YACf,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI;YACxB,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM;SAC7B,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAlDW,QAAA,oBAAoB,wBAkD/B;AAGK,MAAM,iBAAiB,GAAG,CAAC,UAAkB,EAAE,EAAE;IACtD,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;gBACpB,MAAM,IAAA,0BAAW,EAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC;YAC7D,CAAC;YAED,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;YAGrC,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,kCAAkC,CAAC,CAAC;YAEpE,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5E,MAAM,IAAA,0BAAW,EAAC,6BAA6B,UAAU,WAAW,EAAE,GAAG,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAC;QACd,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AArBW,QAAA,iBAAiB,qBAqB5B;AAGK,MAAM,WAAW,GAAG,CAAC,WAAoD,EAAE,EAAE;IAClF,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;gBACpB,MAAM,IAAA,0BAAW,EAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC;YAC7D,CAAC;YAED,MAAM,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC,kCAAkC,CAAC,CAAC;YACvE,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,iBAAiB,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC;YAEtD,IAAI,aAAa,GAAG,iBAAiB,EAAE,CAAC;gBACtC,MAAM,IAAA,0BAAW,EAAC,sBAAsB,WAAW,qBAAqB,EAAE,GAAG,CAAC,CAAC;YACjF,CAAC;YAED,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAC;QACd,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AApBW,QAAA,WAAW,eAoBtB;AAGK,MAAM,YAAY,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACpF,IAAI,CAAC;QACH,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACtC,MAAM,IAAI,GAAG,MAAM,eAAE;iBAClB,MAAM,CAAC;gBACN,EAAE,EAAE,cAAK,CAAC,EAAE;gBACZ,KAAK,EAAE,cAAK,CAAC,KAAK;gBAClB,SAAS,EAAE,cAAK,CAAC,SAAS;gBAC1B,QAAQ,EAAE,cAAK,CAAC,QAAQ;gBACxB,MAAM,EAAE,cAAK,CAAC,MAAM;aACrB,CAAC;iBACD,IAAI,CAAC,cAAK,CAAC;iBACX,KAAK,CAAC,IAAA,gBAAE,EAAC,cAAK,CAAC,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;iBACvC,KAAK,CAAC,CAAC,CAAC,CAAC;YAEZ,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC/C,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC;QACH,CAAC;QACD,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAEf,IAAI,EAAE,CAAC;IACT,CAAC;AACH,CAAC,CAAC;AAxBW,QAAA,YAAY,gBAwBvB"}