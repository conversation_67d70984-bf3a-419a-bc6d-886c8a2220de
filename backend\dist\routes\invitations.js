"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const invitationService_1 = require("../services/invitationService");
const schema_1 = require("../db/schema");
const router = (0, express_1.Router)();
router.get('/:token', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { token } = req.params;
    const invitationData = await invitationService_1.invitationService.getInvitationByToken(token);
    res.json({
        success: true,
        data: {
            invitation: {
                email: invitationData.invitation.email,
                role: invitationData.invitation.role,
                expiresAt: invitationData.invitation.expiresAt,
                hasTemporaryPassword: !!invitationData.invitation.tempPassword,
            },
            organization: invitationData.organization,
        },
    });
}));
router.post('/accept/:token', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { token } = req.params;
    const validatedData = schema_1.acceptInvitationSchema.parse(req.body);
    const result = await invitationService_1.invitationService.acceptInvitation(token, validatedData);
    req.session.userId = result.user.id;
    req.session.organizationId = result.organization.id;
    res.json({
        success: true,
        data: result,
    });
}));
router.post('/decline/:token', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { token } = req.params;
    const result = await invitationService_1.invitationService.declineInvitation(token);
    res.json({
        success: true,
        data: result,
    });
}));
router.post('/organization/:organizationId/invite', auth_1.validateOrganization, (0, auth_1.requirePermission)('org:invite_members'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { organizationId } = req.params;
    const { email, role } = schema_1.insertInvitationSchema.parse({
        ...req.body,
        organizationId,
        invitedBy: req.user.id,
    });
    const result = await invitationService_1.invitationService.sendInvitation(organizationId, email, role, req.user.id);
    res.status(201).json({
        success: true,
        data: result,
    });
}));
router.get('/organization/:organizationId', auth_1.validateOrganization, (0, auth_1.requirePermission)('org:invite_members'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { organizationId } = req.params;
    const invitations = await invitationService_1.invitationService.getOrganizationInvitations(organizationId);
    res.json({
        success: true,
        data: {
            invitations,
        },
    });
}));
router.post('/resend', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { invitationId } = schema_1.resendInvitationSchema.parse(req.body);
    const result = await invitationService_1.invitationService.resendInvitation(invitationId, req.user.id);
    res.json({
        success: true,
        data: result,
    });
}));
router.post('/cleanup-expired', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const result = await invitationService_1.invitationService.cleanupExpiredInvitations();
    res.json({
        success: true,
        data: result,
    });
}));
exports.default = router;
//# sourceMappingURL=invitations.js.map