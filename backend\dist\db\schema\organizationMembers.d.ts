import { z } from 'zod';
export declare const roleEnum: import("drizzle-orm/pg-core").PgEnum<["viewer", "member", "admin", "owner"]>;
export declare const memberStatusEnum: import("drizzle-orm/pg-core").PgEnum<["active", "inactive", "pending"]>;
export declare const organizationMembers: import("drizzle-orm/pg-core").PgTableWithColumns<{
    name: "organization_members";
    schema: undefined;
    columns: {
        id: import("drizzle-orm/pg-core").PgColumn<{
            name: "id";
            tableName: "organization_members";
            dataType: "string";
            columnType: "PgUUID";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: true;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        organizationId: import("drizzle-orm/pg-core").PgColumn<{
            name: "organization_id";
            tableName: "organization_members";
            dataType: "string";
            columnType: "PgUUID";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        userId: import("drizzle-orm/pg-core").PgColumn<{
            name: "user_id";
            tableName: "organization_members";
            dataType: "string";
            columnType: "PgUUID";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        role: import("drizzle-orm/pg-core").PgColumn<{
            name: "role";
            tableName: "organization_members";
            dataType: "string";
            columnType: "PgEnumColumn";
            data: "viewer" | "member" | "admin" | "owner";
            driverParam: string;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: ["viewer", "member", "admin", "owner"];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        status: import("drizzle-orm/pg-core").PgColumn<{
            name: "status";
            tableName: "organization_members";
            dataType: "string";
            columnType: "PgEnumColumn";
            data: "active" | "inactive" | "pending";
            driverParam: string;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: ["active", "inactive", "pending"];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        invitedBy: import("drizzle-orm/pg-core").PgColumn<{
            name: "invited_by";
            tableName: "organization_members";
            dataType: "string";
            columnType: "PgUUID";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        joinedAt: import("drizzle-orm/pg-core").PgColumn<{
            name: "joined_at";
            tableName: "organization_members";
            dataType: "date";
            columnType: "PgTimestamp";
            data: Date;
            driverParam: string;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        createdAt: import("drizzle-orm/pg-core").PgColumn<{
            name: "created_at";
            tableName: "organization_members";
            dataType: "date";
            columnType: "PgTimestamp";
            data: Date;
            driverParam: string;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        updatedAt: import("drizzle-orm/pg-core").PgColumn<{
            name: "updated_at";
            tableName: "organization_members";
            dataType: "date";
            columnType: "PgTimestamp";
            data: Date;
            driverParam: string;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
    };
    dialect: "pg";
}>;
export declare const insertOrganizationMemberSchema: import("drizzle-zod").BuildSchema<"insert", {
    id: import("drizzle-orm/pg-core").PgColumn<{
        name: "id";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgUUID";
        data: string;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: true;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    organizationId: import("drizzle-orm/pg-core").PgColumn<{
        name: "organization_id";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgUUID";
        data: string;
        driverParam: string;
        notNull: true;
        hasDefault: false;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    userId: import("drizzle-orm/pg-core").PgColumn<{
        name: "user_id";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgUUID";
        data: string;
        driverParam: string;
        notNull: true;
        hasDefault: false;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    role: import("drizzle-orm/pg-core").PgColumn<{
        name: "role";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgEnumColumn";
        data: "viewer" | "member" | "admin" | "owner";
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: ["viewer", "member", "admin", "owner"];
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    status: import("drizzle-orm/pg-core").PgColumn<{
        name: "status";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgEnumColumn";
        data: "active" | "inactive" | "pending";
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: ["active", "inactive", "pending"];
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    invitedBy: import("drizzle-orm/pg-core").PgColumn<{
        name: "invited_by";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgUUID";
        data: string;
        driverParam: string;
        notNull: false;
        hasDefault: false;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    joinedAt: import("drizzle-orm/pg-core").PgColumn<{
        name: "joined_at";
        tableName: "organization_members";
        dataType: "date";
        columnType: "PgTimestamp";
        data: Date;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    createdAt: import("drizzle-orm/pg-core").PgColumn<{
        name: "created_at";
        tableName: "organization_members";
        dataType: "date";
        columnType: "PgTimestamp";
        data: Date;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    updatedAt: import("drizzle-orm/pg-core").PgColumn<{
        name: "updated_at";
        tableName: "organization_members";
        dataType: "date";
        columnType: "PgTimestamp";
        data: Date;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
}, import("drizzle-zod").BuildRefine<Pick<{
    id: import("drizzle-orm/pg-core").PgColumn<{
        name: "id";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgUUID";
        data: string;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: true;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    organizationId: import("drizzle-orm/pg-core").PgColumn<{
        name: "organization_id";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgUUID";
        data: string;
        driverParam: string;
        notNull: true;
        hasDefault: false;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    userId: import("drizzle-orm/pg-core").PgColumn<{
        name: "user_id";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgUUID";
        data: string;
        driverParam: string;
        notNull: true;
        hasDefault: false;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    role: import("drizzle-orm/pg-core").PgColumn<{
        name: "role";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgEnumColumn";
        data: "viewer" | "member" | "admin" | "owner";
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: ["viewer", "member", "admin", "owner"];
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    status: import("drizzle-orm/pg-core").PgColumn<{
        name: "status";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgEnumColumn";
        data: "active" | "inactive" | "pending";
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: ["active", "inactive", "pending"];
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    invitedBy: import("drizzle-orm/pg-core").PgColumn<{
        name: "invited_by";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgUUID";
        data: string;
        driverParam: string;
        notNull: false;
        hasDefault: false;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    joinedAt: import("drizzle-orm/pg-core").PgColumn<{
        name: "joined_at";
        tableName: "organization_members";
        dataType: "date";
        columnType: "PgTimestamp";
        data: Date;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    createdAt: import("drizzle-orm/pg-core").PgColumn<{
        name: "created_at";
        tableName: "organization_members";
        dataType: "date";
        columnType: "PgTimestamp";
        data: Date;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    updatedAt: import("drizzle-orm/pg-core").PgColumn<{
        name: "updated_at";
        tableName: "organization_members";
        dataType: "date";
        columnType: "PgTimestamp";
        data: Date;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
}, "id" | "createdAt" | "updatedAt" | "status" | "role" | "organizationId" | "userId" | "invitedBy" | "joinedAt">, undefined>, undefined>;
export declare const selectOrganizationMemberSchema: import("drizzle-zod").BuildSchema<"select", {
    id: import("drizzle-orm/pg-core").PgColumn<{
        name: "id";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgUUID";
        data: string;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: true;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    organizationId: import("drizzle-orm/pg-core").PgColumn<{
        name: "organization_id";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgUUID";
        data: string;
        driverParam: string;
        notNull: true;
        hasDefault: false;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    userId: import("drizzle-orm/pg-core").PgColumn<{
        name: "user_id";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgUUID";
        data: string;
        driverParam: string;
        notNull: true;
        hasDefault: false;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    role: import("drizzle-orm/pg-core").PgColumn<{
        name: "role";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgEnumColumn";
        data: "viewer" | "member" | "admin" | "owner";
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: ["viewer", "member", "admin", "owner"];
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    status: import("drizzle-orm/pg-core").PgColumn<{
        name: "status";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgEnumColumn";
        data: "active" | "inactive" | "pending";
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: ["active", "inactive", "pending"];
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    invitedBy: import("drizzle-orm/pg-core").PgColumn<{
        name: "invited_by";
        tableName: "organization_members";
        dataType: "string";
        columnType: "PgUUID";
        data: string;
        driverParam: string;
        notNull: false;
        hasDefault: false;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    joinedAt: import("drizzle-orm/pg-core").PgColumn<{
        name: "joined_at";
        tableName: "organization_members";
        dataType: "date";
        columnType: "PgTimestamp";
        data: Date;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    createdAt: import("drizzle-orm/pg-core").PgColumn<{
        name: "created_at";
        tableName: "organization_members";
        dataType: "date";
        columnType: "PgTimestamp";
        data: Date;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    updatedAt: import("drizzle-orm/pg-core").PgColumn<{
        name: "updated_at";
        tableName: "organization_members";
        dataType: "date";
        columnType: "PgTimestamp";
        data: Date;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
}, undefined, undefined>;
export declare const updateOrganizationMemberSchema: z.ZodObject<{
    role: z.ZodOptional<z.ZodEnum<["viewer", "member", "admin", "owner"]>>;
    status: z.ZodOptional<z.ZodEnum<["active", "inactive", "pending"]>>;
}, "strip", z.ZodTypeAny, {
    status?: "active" | "inactive" | "pending" | undefined;
    role?: "viewer" | "member" | "admin" | "owner" | undefined;
}, {
    status?: "active" | "inactive" | "pending" | undefined;
    role?: "viewer" | "member" | "admin" | "owner" | undefined;
}>;
export declare const ROLE_HIERARCHY: {
    readonly viewer: 0;
    readonly member: 1;
    readonly admin: 2;
    readonly owner: 3;
};
export declare const PERMISSIONS: {
    readonly 'org:read': readonly ["viewer", "member", "admin", "owner"];
    readonly 'org:update': readonly ["admin", "owner"];
    readonly 'org:delete': readonly ["owner"];
    readonly 'org:manage_members': readonly ["admin", "owner"];
    readonly 'org:invite_members': readonly ["admin", "owner"];
    readonly 'board:read': readonly ["viewer", "member", "admin", "owner"];
    readonly 'board:create': readonly ["member", "admin", "owner"];
    readonly 'board:update': readonly ["admin", "owner"];
    readonly 'board:delete': readonly ["admin", "owner"];
    readonly 'board:manage_columns': readonly ["admin", "owner"];
    readonly 'card:read': readonly ["viewer", "member", "admin", "owner"];
    readonly 'card:create': readonly ["member", "admin", "owner"];
    readonly 'card:update': readonly ["member", "admin", "owner"];
    readonly 'card:delete': readonly ["member", "admin", "owner"];
    readonly 'card:assign': readonly ["member", "admin", "owner"];
    readonly 'comment:read': readonly ["viewer", "member", "admin", "owner"];
    readonly 'comment:create': readonly ["member", "admin", "owner"];
    readonly 'comment:update': readonly ["member", "admin", "owner"];
    readonly 'comment:delete': readonly ["member", "admin", "owner"];
};
export type OrganizationMember = typeof organizationMembers.$inferSelect;
export type NewOrganizationMember = typeof organizationMembers.$inferInsert;
export type UpdateOrganizationMember = z.infer<typeof updateOrganizationMemberSchema>;
export type Role = z.infer<typeof roleEnum.enumValues>;
export type MemberStatus = z.infer<typeof memberStatusEnum.enumValues>;
export type Permission = keyof typeof PERMISSIONS;
//# sourceMappingURL=organizationMembers.d.ts.map