"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const morgan_1 = __importDefault(require("morgan"));
const express_session_1 = __importDefault(require("express-session"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const dotenv_1 = __importDefault(require("dotenv"));
const env_1 = require("./utils/env");
dotenv_1.default.config();
const errorHandler_1 = require("./middleware/errorHandler");
const notFoundHandler_1 = require("./middleware/notFoundHandler");
const auth_1 = require("./middleware/auth");
const auth_2 = __importDefault(require("./routes/auth"));
const organizations_1 = __importDefault(require("./routes/organizations"));
const users_1 = __importDefault(require("./routes/users"));
const boards_1 = __importDefault(require("./routes/boards"));
const cards_1 = __importDefault(require("./routes/cards"));
const invitations_1 = __importDefault(require("./routes/invitations"));
const app = (0, express_1.default)();
app.set('trust proxy', 1);
app.use((0, helmet_1.default)({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
}));
const limiter = (0, express_rate_limit_1.default)({
    windowMs: env_1.env.RATE_LIMIT_WINDOW_MS,
    max: env_1.env.RATE_LIMIT_MAX_REQUESTS,
    message: {
        error: 'Too many requests from this IP, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use(limiter);
app.use((0, cors_1.default)({
    origin: env_1.env.CORS_ORIGIN,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));
app.use((0, compression_1.default)());
app.use((0, morgan_1.default)('combined'));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
const sessionConfig = {
    secret: env_1.env.SESSION_SECRET,
    name: env_1.env.SESSION_NAME,
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: env_1.env.NODE_ENV === 'production',
        httpOnly: true,
        maxAge: env_1.env.SESSION_MAX_AGE,
        sameSite: env_1.env.NODE_ENV === 'production' ? 'strict' : 'lax',
    },
};
if (env_1.env.NODE_ENV === 'production') {
    const connectPgSimple = require('connect-pg-simple');
    const pgSession = connectPgSimple(express_session_1.default);
    sessionConfig.store = new pgSession({
        conString: env_1.env.DATABASE_URL,
        tableName: 'user_sessions',
        createTableIfMissing: true,
    });
}
app.use((0, express_session_1.default)(sessionConfig));
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: env_1.env.NODE_ENV,
    });
});
app.use('/api/auth', auth_2.default);
app.use('/api/organizations', auth_1.validateSession, organizations_1.default);
app.use('/api/users', auth_1.validateSession, users_1.default);
app.use('/api/boards', auth_1.validateSession, boards_1.default);
app.use('/api/cards', auth_1.validateSession, cards_1.default);
app.use('/api/invitations', invitations_1.default);
app.use(notFoundHandler_1.notFoundHandler);
app.use(errorHandler_1.errorHandler);
const server = app.listen(env_1.env.PORT, () => {
    console.log(`🚀 Agno WorkSphere Backend running on port ${env_1.env.PORT}`);
    console.log(`📊 Environment: ${env_1.env.NODE_ENV}`);
    console.log(`🔗 API Base URL: http://localhost:${env_1.env.PORT}`);
});
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('Process terminated');
    });
});
process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('Process terminated');
    });
});
exports.default = app;
//# sourceMappingURL=server.js.map