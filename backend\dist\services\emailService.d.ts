export interface EmailTemplate {
    to: string;
    subject: string;
    html: string;
    text?: string;
}
export declare class EmailService {
    private readonly fromEmail;
    private readonly fromName;
    private transporter;
    constructor();
    sendEmail(template: EmailTemplate): Promise<void>;
    generateInvitationEmail(inviteeEmail: string, organizationName: string, inviterName: string, role: string, invitationToken: string, tempPassword?: string): EmailTemplate;
    generateWelcomeEmail(userEmail: string, userName: string, organizationName: string): EmailTemplate;
    private getRolePermissions;
    private stripHtml;
}
export declare const emailService: EmailService;
//# sourceMappingURL=emailService.d.ts.map