"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateOrganizationSchema = exports.selectOrganizationSchema = exports.insertOrganizationSchema = exports.organizations = void 0;
const pg_core_1 = require("drizzle-orm/pg-core");
const zod_1 = require("zod");
exports.organizations = (0, pg_core_1.pgTable)('organizations', {
    id: (0, pg_core_1.uuid)('id').primaryKey().defaultRandom(),
    name: (0, pg_core_1.varchar)('name', { length: 255 }).notNull(),
    slug: (0, pg_core_1.varchar)('slug', { length: 100 }).notNull().unique(),
    domain: (0, pg_core_1.varchar)('domain', { length: 255 }).unique(),
    description: (0, pg_core_1.text)('description'),
    logo: (0, pg_core_1.varchar)('logo', { length: 500 }),
    settings: (0, pg_core_1.jsonb)('settings').$type().default({}),
    isActive: (0, pg_core_1.boolean)('is_active').default(true).notNull(),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow().notNull(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').defaultNow().notNull(),
});
exports.insertOrganizationSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Organization name is required').max(255),
    slug: zod_1.z.string().min(1, 'Slug is required').max(100).regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens').optional(),
    domain: zod_1.z.string().optional(),
    description: zod_1.z.string().max(1000).optional(),
    logo: zod_1.z.string().url('Invalid logo URL').optional(),
    isActive: zod_1.z.boolean().default(true),
});
exports.selectOrganizationSchema = zod_1.z.object({
    id: zod_1.z.string(),
    name: zod_1.z.string(),
    slug: zod_1.z.string(),
    domain: zod_1.z.string().nullable(),
    description: zod_1.z.string().nullable(),
    logo: zod_1.z.string().nullable(),
    settings: zod_1.z.record(zod_1.z.any()).nullable(),
    isActive: zod_1.z.boolean(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date(),
});
exports.updateOrganizationSchema = exports.insertOrganizationSchema.partial();
//# sourceMappingURL=organizations.js.map