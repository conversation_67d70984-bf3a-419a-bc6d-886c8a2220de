{"version": 3, "sources": ["../../react-dnd-html5-backend/src/utils/js_utils.ts", "../../react-dnd-html5-backend/src/EnterLeaveCounter.ts", "../../react-dnd-html5-backend/src/NativeDragSources/NativeDragSource.ts", "../../react-dnd-html5-backend/src/NativeTypes.ts", "../../react-dnd-html5-backend/src/NativeDragSources/getDataFromDataTransfer.ts", "../../react-dnd-html5-backend/src/NativeDragSources/nativeTypesConfig.ts", "../../react-dnd-html5-backend/src/NativeDragSources/index.ts", "../../react-dnd-html5-backend/src/BrowserDetector.ts", "../../react-dnd-html5-backend/src/MonotonicInterpolant.ts", "../../react-dnd-html5-backend/src/OffsetUtils.ts", "../../react-dnd-html5-backend/src/OptionsReader.ts", "../../react-dnd-html5-backend/src/HTML5BackendImpl.ts", "../../react-dnd-html5-backend/src/getEmptyImage.ts", "../../react-dnd-html5-backend/src/index.ts"], "sourcesContent": ["// cheap lodash replacements\n\nexport function memoize<T>(fn: () => T): () => T {\n\tlet result: T | null = null\n\tconst memoized = () => {\n\t\tif (result == null) {\n\t\t\tresult = fn()\n\t\t}\n\t\treturn result\n\t}\n\treturn memoized\n}\n\n/**\n * drop-in replacement for _.without\n */\nexport function without<T>(items: T[], item: T) {\n\treturn items.filter((i) => i !== item)\n}\n\nexport function union<T extends string | number>(itemsA: T[], itemsB: T[]) {\n\tconst set = new Set<T>()\n\tconst insertItem = (item: T) => set.add(item)\n\titemsA.forEach(insertItem)\n\titemsB.forEach(insertItem)\n\n\tconst result: T[] = []\n\tset.forEach((key) => result.push(key))\n\treturn result\n}\n", "import { union, without } from './utils/js_utils.js'\n\ntype NodePredicate = (node: Node | null | undefined) => boolean\n\nexport class EnterLeaveCounter {\n\tprivate entered: any[] = []\n\tprivate isNodeInDocument: NodePredicate\n\n\tpublic constructor(isNodeInDocument: NodePredicate) {\n\t\tthis.isNodeInDocument = isNodeInDocument\n\t}\n\n\tpublic enter(enteringNode: EventTarget | null): boolean {\n\t\tconst previousLength = this.entered.length\n\n\t\tconst isNodeEntered = (node: Node): boolean =>\n\t\t\tthis.isNodeInDocument(node) &&\n\t\t\t(!node.contains || node.contains(enteringNode as Node))\n\n\t\tthis.entered = union(this.entered.filter(isNodeEntered), [enteringNode])\n\n\t\treturn previousLength === 0 && this.entered.length > 0\n\t}\n\n\tpublic leave(leavingNode: EventTarget | null): boolean {\n\t\tconst previousLength = this.entered.length\n\n\t\tthis.entered = without(\n\t\t\tthis.entered.filter(this.isNodeInDocument),\n\t\t\tleavingNode,\n\t\t)\n\n\t\treturn previousLength > 0 && this.entered.length === 0\n\t}\n\n\tpublic reset(): void {\n\t\tthis.entered = []\n\t}\n}\n", "import type { DragDropMonitor } from 'dnd-core'\n\nimport type { NativeItemConfig } from './nativeTypesConfig.js'\n\nexport class NativeDragSource {\n\tpublic item: any\n\tprivate config: NativeItemConfig\n\n\tpublic constructor(config: NativeItemConfig) {\n\t\tthis.config = config\n\t\tthis.item = {}\n\t\tthis.initializeExposedProperties()\n\t}\n\n\tprivate initializeExposedProperties() {\n\t\tObject.keys(this.config.exposeProperties).forEach((property) => {\n\t\t\tObject.defineProperty(this.item, property, {\n\t\t\t\tconfigurable: true, // This is needed to allow redefining it later\n\t\t\t\tenumerable: true,\n\t\t\t\tget() {\n\t\t\t\t\t// eslint-disable-next-line no-console\n\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\t`Browser doesn't allow reading \"${property}\" until the drop event.`,\n\t\t\t\t\t)\n\t\t\t\t\treturn null\n\t\t\t\t},\n\t\t\t})\n\t\t})\n\t}\n\n\tpublic loadDataTransfer(dataTransfer: DataTransfer | null | undefined): void {\n\t\tif (dataTransfer) {\n\t\t\tconst newProperties: PropertyDescriptorMap = {}\n\t\t\tObject.keys(this.config.exposeProperties).forEach((property) => {\n\t\t\t\tconst propertyFn = this.config.exposeProperties[property]\n\t\t\t\tif (propertyFn != null) {\n\t\t\t\t\tnewProperties[property] = {\n\t\t\t\t\t\tvalue: propertyFn(dataTransfer, this.config.matchesTypes),\n\t\t\t\t\t\tconfigurable: true,\n\t\t\t\t\t\tenumerable: true,\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t\tObject.defineProperties(this.item, newProperties)\n\t\t}\n\t}\n\n\tpublic canDrag(): boolean {\n\t\treturn true\n\t}\n\n\tpublic beginDrag(): any {\n\t\treturn this.item\n\t}\n\n\tpublic isDragging(monitor: DragDropMonitor, handle: string): boolean {\n\t\treturn handle === monitor.getSourceId()\n\t}\n\n\tpublic endDrag(): void {\n\t\t// empty\n\t}\n}\n", "export const FILE = '__NATIVE_FILE__'\nexport const URL = '__NATIVE_URL__'\nexport const TEXT = '__NATIVE_TEXT__'\nexport const HTML = '__NATIVE_HTML__'\n", "export function getDataFromDataTransfer(\n\tdataTransfer: DataTransfer,\n\ttypesToTry: string[],\n\tdefaultValue: string,\n): string {\n\tconst result = typesToTry.reduce(\n\t\t(resultSoFar, typeToTry) => resultSoFar || dataTransfer.getData(typeToTry),\n\t\t'',\n\t)\n\n\treturn result != null ? result : defaultValue\n}\n", "import * as NativeTypes from '../NativeTypes.js'\nimport { getDataFromDataTransfer } from './getDataFromDataTransfer.js'\n\nexport interface NativeItemConfigExposePropreties {\n\t[property: string]: (\n\t\tdataTransfer: DataTransfer,\n\t\tmatchesTypes: string[],\n\t) => any\n}\n\nexport interface NativeItemConfig {\n\texposeProperties: NativeItemConfigExposePropreties\n\tmatchesTypes: string[]\n}\n\nexport const nativeTypesConfig: {\n\t[key: string]: NativeItemConfig\n} = {\n\t[NativeTypes.FILE]: {\n\t\texposeProperties: {\n\t\t\tfiles: (dataTransfer: DataTransfer): File[] =>\n\t\t\t\tArray.prototype.slice.call(dataTransfer.files),\n\t\t\titems: (dataTransfer: DataTransfer): DataTransferItemList =>\n\t\t\t\tdataTransfer.items,\n\t\t\tdataTransfer: (dataTransfer: DataTransfer): DataTransfer => dataTransfer,\n\t\t},\n\t\tmatchesTypes: ['Files'],\n\t},\n\t[NativeTypes.HTML]: {\n\t\texposeProperties: {\n\t\t\thtml: (dataTransfer: DataTransfer, matchesTypes: string[]): string =>\n\t\t\t\tgetDataFromDataTransfer(dataTransfer, matchesTypes, ''),\n\t\t\tdataTransfer: (dataTransfer: DataTransfer): DataTransfer => dataTransfer,\n\t\t},\n\t\tmatchesTypes: ['Html', 'text/html'],\n\t},\n\t[NativeTypes.URL]: {\n\t\texposeProperties: {\n\t\t\turls: (dataTransfer: DataTransfer, matchesTypes: string[]): string[] =>\n\t\t\t\tgetDataFromDataTransfer(dataTransfer, matchesTypes, '').split('\\n'),\n\t\t\tdataTransfer: (dataTransfer: DataTransfer): DataTransfer => dataTransfer,\n\t\t},\n\t\tmatchesTypes: ['Url', 'text/uri-list'],\n\t},\n\t[NativeTypes.TEXT]: {\n\t\texposeProperties: {\n\t\t\ttext: (dataTransfer: DataTransfer, matchesTypes: string[]): string =>\n\t\t\t\tgetDataFromDataTransfer(dataTransfer, matchesTypes, ''),\n\t\t\tdataTransfer: (dataTransfer: DataTransfer): DataTransfer => dataTransfer,\n\t\t},\n\t\tmatchesTypes: ['Text', 'text/plain'],\n\t},\n}\n", "import { NativeDragSource } from './NativeDragSource.js'\nimport { nativeTypesConfig } from './nativeTypesConfig.js'\n\nexport function createNativeDragSource(\n\ttype: string,\n\tdataTransfer?: DataTransfer,\n): NativeDragSource {\n\tconst config = nativeTypesConfig[type]\n\tif (!config) {\n\t\tthrow new Error(`native type ${type} has no configuration`)\n\t}\n\tconst result = new NativeDragSource(config)\n\tresult.loadDataTransfer(dataTransfer)\n\treturn result\n}\n\nexport function matchNativeItemType(\n\tdataTransfer: DataTransfer | null,\n): string | null {\n\tif (!dataTransfer) {\n\t\treturn null\n\t}\n\n\tconst dataTransferTypes = Array.prototype.slice.call(dataTransfer.types || [])\n\treturn (\n\t\tObject.keys(nativeTypesConfig).filter((nativeItemType) => {\n\t\t\tconst typeConfig = nativeTypesConfig[nativeItemType]\n\t\t\tif (!typeConfig?.matchesTypes) {\n\t\t\t\treturn false\n\t\t\t}\n\t\t\treturn typeConfig.matchesTypes.some(\n\t\t\t\t(t) => dataTransferTypes.indexOf(t) > -1,\n\t\t\t)\n\t\t})[0] || null\n\t)\n}\n", "import { memoize } from './utils/js_utils.js'\n\ndeclare global {\n\tinterface Window extends HTMLElement {\n\t\tsafari: any\n\t}\n}\n\nexport type Predicate = () => boolean\nexport const isFirefox: Predicate = memoize(() =>\n\t/firefox/i.test(navigator.userAgent),\n)\nexport const isSafari: Predicate = memoize(() => Boolean(window.safari))\n", "export class MonotonicInterpolant {\n\tprivate xs: any\n\tprivate ys: any\n\tprivate c1s: any\n\tprivate c2s: any\n\tprivate c3s: any\n\n\tpublic constructor(xs: number[], ys: number[]) {\n\t\tconst { length } = xs\n\n\t\t// Rearrange xs and ys so that xs is sorted\n\t\tconst indexes = []\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tindexes.push(i)\n\t\t}\n\t\tindexes.sort((a, b) => ((xs[a] as number) < (xs[b] as number) ? -1 : 1))\n\n\t\t// Get consecutive differences and slopes\n\t\tconst dys = []\n\t\tconst dxs = []\n\t\tconst ms = []\n\t\tlet dx\n\t\tlet dy\n\t\tfor (let i = 0; i < length - 1; i++) {\n\t\t\tdx = (xs[i + 1] as number) - (xs[i] as number)\n\t\t\tdy = (ys[i + 1] as number) - (ys[i] as number)\n\t\t\tdxs.push(dx)\n\t\t\tdys.push(dy)\n\t\t\tms.push(dy / dx)\n\t\t}\n\n\t\t// Get degree-1 coefficients\n\t\tconst c1s = [ms[0]]\n\t\tfor (let i = 0; i < dxs.length - 1; i++) {\n\t\t\tconst m2 = ms[i] as number\n\t\t\tconst mNext = ms[i + 1] as number\n\t\t\tif (m2 * mNext <= 0) {\n\t\t\t\tc1s.push(0)\n\t\t\t} else {\n\t\t\t\tdx = dxs[i] as number\n\t\t\t\tconst dxNext = dxs[i + 1] as number\n\t\t\t\tconst common = dx + dxNext\n\t\t\t\tc1s.push(\n\t\t\t\t\t(3 * common) / ((common + dxNext) / m2 + (common + dx) / mNext),\n\t\t\t\t)\n\t\t\t}\n\t\t}\n\t\tc1s.push(ms[ms.length - 1])\n\n\t\t// Get degree-2 and degree-3 coefficients\n\t\tconst c2s = []\n\t\tconst c3s = []\n\t\tlet m\n\t\tfor (let i = 0; i < c1s.length - 1; i++) {\n\t\t\tm = ms[i] as number\n\t\t\tconst c1 = c1s[i] as number\n\t\t\tconst invDx = 1 / (dxs[i] as number)\n\t\t\tconst common = c1 + (c1s[i + 1] as number) - m - m\n\t\t\tc2s.push((m - c1 - common) * invDx)\n\t\t\tc3s.push(common * invDx * invDx)\n\t\t}\n\n\t\tthis.xs = xs\n\t\tthis.ys = ys\n\t\tthis.c1s = c1s\n\t\tthis.c2s = c2s\n\t\tthis.c3s = c3s\n\t}\n\n\tpublic interpolate(x: number): number {\n\t\tconst { xs, ys, c1s, c2s, c3s } = this\n\n\t\t// The rightmost point in the dataset should give an exact result\n\t\tlet i = xs.length - 1\n\t\tif (x === xs[i]) {\n\t\t\treturn ys[i]\n\t\t}\n\n\t\t// Search for the interval x is in, returning the corresponding y if x is one of the original xs\n\t\tlet low = 0\n\t\tlet high = c3s.length - 1\n\t\tlet mid\n\t\twhile (low <= high) {\n\t\t\tmid = Math.floor(0.5 * (low + high))\n\t\t\tconst xHere = xs[mid]\n\t\t\tif (xHere < x) {\n\t\t\t\tlow = mid + 1\n\t\t\t} else if (xHere > x) {\n\t\t\t\thigh = mid - 1\n\t\t\t} else {\n\t\t\t\treturn ys[mid]\n\t\t\t}\n\t\t}\n\t\ti = Math.max(0, high)\n\n\t\t// Interpolate\n\t\tconst diff = x - xs[i]\n\t\tconst diffSq = diff * diff\n\t\treturn ys[i] + c1s[i] * diff + c2s[i] * diffSq + c3s[i] * diff * diffSq\n\t}\n}\n", "import type { XYCoord } from 'dnd-core'\n\nimport { isFirefox, isSafari } from './BrowserDetector.js'\nimport { MonotonicInterpolant } from './MonotonicInterpolant.js'\n\nconst ELEMENT_NODE = 1\n\nexport function getNodeClientOffset(node: Node): XYCoord | null {\n\tconst el = node.nodeType === ELEMENT_NODE ? node : node.parentElement\n\n\tif (!el) {\n\t\treturn null\n\t}\n\n\tconst { top, left } = (el as HTMLElement).getBoundingClientRect()\n\treturn { x: left, y: top }\n}\n\nexport function getEventClientOffset(e: MouseEvent): XYCoord {\n\treturn {\n\t\tx: e.clientX,\n\t\ty: e.clientY,\n\t}\n}\n\nfunction isImageNode(node: any) {\n\treturn (\n\t\tnode.nodeName === 'IMG' &&\n\t\t(isFirefox() || !document.documentElement?.contains(node))\n\t)\n}\n\nfunction getDragPreviewSize(\n\tisImage: boolean,\n\tdragPreview: any,\n\tsourceWidth: number,\n\tsourceHeight: number,\n) {\n\tlet dragPreviewWidth = isImage ? dragPreview.width : sourceWidth\n\tlet dragPreviewHeight = isImage ? dragPreview.height : sourceHeight\n\n\t// Work around @2x coordinate discrepancies in browsers\n\tif (isSafari() && isImage) {\n\t\tdragPreviewHeight /= window.devicePixelRatio\n\t\tdragPreviewWidth /= window.devicePixelRatio\n\t}\n\treturn { dragPreviewWidth, dragPreviewHeight }\n}\n\nexport function getDragPreviewOffset(\n\tsourceNode: HTMLElement,\n\tdragPreview: HTMLElement,\n\tclientOffset: XYCoord,\n\tanchorPoint: { anchorX: number; anchorY: number },\n\toffsetPoint: { offsetX: number; offsetY: number },\n): XYCoord {\n\t// The browsers will use the image intrinsic size under different conditions.\n\t// Firefox only cares if it's an image, but WebKit also wants it to be detached.\n\tconst isImage = isImageNode(dragPreview)\n\tconst dragPreviewNode = isImage ? sourceNode : dragPreview\n\tconst dragPreviewNodeOffsetFromClient = getNodeClientOffset(\n\t\tdragPreviewNode,\n\t) as XYCoord\n\tconst offsetFromDragPreview = {\n\t\tx: clientOffset.x - dragPreviewNodeOffsetFromClient.x,\n\t\ty: clientOffset.y - dragPreviewNodeOffsetFromClient.y,\n\t}\n\tconst { offsetWidth: sourceWidth, offsetHeight: sourceHeight } = sourceNode\n\tconst { anchorX, anchorY } = anchorPoint\n\tconst { dragPreviewWidth, dragPreviewHeight } = getDragPreviewSize(\n\t\tisImage,\n\t\tdragPreview,\n\t\tsourceWidth,\n\t\tsourceHeight,\n\t)\n\n\tconst calculateYOffset = () => {\n\t\tconst interpolantY = new MonotonicInterpolant(\n\t\t\t[0, 0.5, 1],\n\t\t\t[\n\t\t\t\t// Dock to the top\n\t\t\t\toffsetFromDragPreview.y,\n\t\t\t\t// Align at the center\n\t\t\t\t(offsetFromDragPreview.y / sourceHeight) * dragPreviewHeight,\n\t\t\t\t// Dock to the bottom\n\t\t\t\toffsetFromDragPreview.y + dragPreviewHeight - sourceHeight,\n\t\t\t],\n\t\t)\n\t\tlet y = interpolantY.interpolate(anchorY)\n\t\t// Work around Safari 8 positioning bug\n\t\tif (isSafari() && isImage) {\n\t\t\t// We'll have to wait for @3x to see if this is entirely correct\n\t\t\ty += (window.devicePixelRatio - 1) * dragPreviewHeight\n\t\t}\n\t\treturn y\n\t}\n\n\tconst calculateXOffset = () => {\n\t\t// Interpolate coordinates depending on anchor point\n\t\t// If you know a simpler way to do this, let me know\n\t\tconst interpolantX = new MonotonicInterpolant(\n\t\t\t[0, 0.5, 1],\n\t\t\t[\n\t\t\t\t// Dock to the left\n\t\t\t\toffsetFromDragPreview.x,\n\t\t\t\t// Align at the center\n\t\t\t\t(offsetFromDragPreview.x / sourceWidth) * dragPreviewWidth,\n\t\t\t\t// Dock to the right\n\t\t\t\toffsetFromDragPreview.x + dragPreviewWidth - sourceWidth,\n\t\t\t],\n\t\t)\n\t\treturn interpolantX.interpolate(anchorX)\n\t}\n\n\t// Force offsets if specified in the options.\n\tconst { offsetX, offsetY } = offsetPoint\n\tconst isManualOffsetX = offsetX === 0 || offsetX\n\tconst isManualOffsetY = offsetY === 0 || offsetY\n\treturn {\n\t\tx: isManualOffsetX ? offsetX : calculateXOffset(),\n\t\ty: isManualOffsetY ? offsetY : calculateYOffset(),\n\t}\n}\n", "import type { HTML5BackendContext, HTML5BackendOptions } from './types.js'\n\nexport class OptionsReader {\n\tpublic ownerDocument: Document | null = null\n\tprivate globalContext: HTML5BackendContext\n\tprivate optionsArgs: HTML5BackendOptions | undefined\n\n\tpublic constructor(\n\t\tglobalContext: HTML5BackendContext,\n\t\toptions?: HTML5BackendOptions,\n\t) {\n\t\tthis.globalContext = globalContext\n\t\tthis.optionsArgs = options\n\t}\n\n\tpublic get window(): Window | undefined {\n\t\tif (this.globalContext) {\n\t\t\treturn this.globalContext\n\t\t} else if (typeof window !== 'undefined') {\n\t\t\treturn window\n\t\t}\n\t\treturn undefined\n\t}\n\n\tpublic get document(): Document | undefined {\n\t\tif (this.globalContext?.document) {\n\t\t\treturn this.globalContext.document\n\t\t} else if (this.window) {\n\t\t\treturn this.window.document\n\t\t} else {\n\t\t\treturn undefined\n\t\t}\n\t}\n\n\tpublic get rootElement(): Node | undefined {\n\t\treturn this.optionsArgs?.rootElement || this.window\n\t}\n}\n", "import type {\n\tBackend,\n\t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n\tDragDropManager,\n\tDragDropMonitor,\n\tHandlerRegistry,\n\tIdentifier,\n\tUnsubscribe,\n\tXYCoord,\n} from 'dnd-core'\n\nimport { EnterLeaveCounter } from './EnterLeaveCounter.js'\nimport {\n\tcreateNativeDragSource,\n\tmatchNativeItemType,\n} from './NativeDragSources/index.js'\nimport type { NativeDragSource } from './NativeDragSources/NativeDragSource.js'\nimport * as NativeTypes from './NativeTypes.js'\nimport {\n\tgetDragPreviewOffset,\n\tgetEventClientOffset,\n\tgetNodeClientOffset,\n} from './OffsetUtils.js'\nimport { OptionsReader } from './OptionsReader.js'\nimport type { HTML5BackendContext, HTML5BackendOptions } from './types.js'\n\ntype RootNode = Node & { __isReactDndBackendSetUp: boolean | undefined }\n\nexport class HTML5BackendImpl implements Backend {\n\tprivate options: OptionsReader\n\n\t// React-Dnd Components\n\tprivate actions: DragDropActions\n\tprivate monitor: DragDropMonitor\n\tprivate registry: HandlerRegistry\n\n\t// Internal State\n\tprivate enterLeaveCounter: EnterLeaveCounter\n\n\tprivate sourcePreviewNodes: Map<string, Element> = new Map()\n\tprivate sourcePreviewNodeOptions: Map<string, any> = new Map()\n\tprivate sourceNodes: Map<string, Element> = new Map()\n\tprivate sourceNodeOptions: Map<string, any> = new Map()\n\n\tprivate dragStartSourceIds: string[] | null = null\n\tprivate dropTargetIds: string[] = []\n\tprivate dragEnterTargetIds: string[] = []\n\tprivate currentNativeSource: NativeDragSource | null = null\n\tprivate currentNativeHandle: Identifier | null = null\n\tprivate currentDragSourceNode: Element | null = null\n\tprivate altKeyPressed = false\n\tprivate mouseMoveTimeoutTimer: number | null = null\n\tprivate asyncEndDragFrameId: number | null = null\n\tprivate dragOverTargetIds: string[] | null = null\n\n\tprivate lastClientOffset: XYCoord | null = null\n\tprivate hoverRafId: number | null = null\n\n\tpublic constructor(\n\t\tmanager: DragDropManager,\n\t\tglobalContext?: HTML5BackendContext,\n\t\toptions?: HTML5BackendOptions,\n\t) {\n\t\tthis.options = new OptionsReader(globalContext, options)\n\t\tthis.actions = manager.getActions()\n\t\tthis.monitor = manager.getMonitor()\n\t\tthis.registry = manager.getRegistry()\n\t\tthis.enterLeaveCounter = new EnterLeaveCounter(this.isNodeInDocument)\n\t}\n\n\t/**\n\t * Generate profiling statistics for the HTML5Backend.\n\t */\n\tpublic profile(): Record<string, number> {\n\t\treturn {\n\t\t\tsourcePreviewNodes: this.sourcePreviewNodes.size,\n\t\t\tsourcePreviewNodeOptions: this.sourcePreviewNodeOptions.size,\n\t\t\tsourceNodeOptions: this.sourceNodeOptions.size,\n\t\t\tsourceNodes: this.sourceNodes.size,\n\t\t\tdragStartSourceIds: this.dragStartSourceIds?.length || 0,\n\t\t\tdropTargetIds: this.dropTargetIds.length,\n\t\t\tdragEnterTargetIds: this.dragEnterTargetIds.length,\n\t\t\tdragOverTargetIds: this.dragOverTargetIds?.length || 0,\n\t\t}\n\t}\n\n\t// public for test\n\tpublic get window(): Window | undefined {\n\t\treturn this.options.window\n\t}\n\tpublic get document(): Document | undefined {\n\t\treturn this.options.document\n\t}\n\t/**\n\t * Get the root element to use for event subscriptions\n\t */\n\tprivate get rootElement(): Node | undefined {\n\t\treturn this.options.rootElement as Node\n\t}\n\n\tpublic setup(): void {\n\t\tconst root = this.rootElement as RootNode | undefined\n\t\tif (root === undefined) {\n\t\t\treturn\n\t\t}\n\n\t\tif (root.__isReactDndBackendSetUp) {\n\t\t\tthrow new Error('Cannot have two HTML5 backends at the same time.')\n\t\t}\n\t\troot.__isReactDndBackendSetUp = true\n\t\tthis.addEventListeners(root)\n\t}\n\n\tpublic teardown(): void {\n\t\tconst root = this.rootElement as RootNode\n\t\tif (root === undefined) {\n\t\t\treturn\n\t\t}\n\n\t\troot.__isReactDndBackendSetUp = false\n\t\tthis.removeEventListeners(this.rootElement as Element)\n\t\tthis.clearCurrentDragSourceNode()\n\t\tif (this.asyncEndDragFrameId) {\n\t\t\tthis.window?.cancelAnimationFrame(this.asyncEndDragFrameId)\n\t\t}\n\t}\n\n\tpublic connectDragPreview(\n\t\tsourceId: string,\n\t\tnode: Element,\n\t\toptions: any,\n\t): Unsubscribe {\n\t\tthis.sourcePreviewNodeOptions.set(sourceId, options)\n\t\tthis.sourcePreviewNodes.set(sourceId, node)\n\n\t\treturn (): void => {\n\t\t\tthis.sourcePreviewNodes.delete(sourceId)\n\t\t\tthis.sourcePreviewNodeOptions.delete(sourceId)\n\t\t}\n\t}\n\n\tpublic connectDragSource(\n\t\tsourceId: string,\n\t\tnode: Element,\n\t\toptions: any,\n\t): Unsubscribe {\n\t\tthis.sourceNodes.set(sourceId, node)\n\t\tthis.sourceNodeOptions.set(sourceId, options)\n\n\t\tconst handleDragStart = (e: any) => this.handleDragStart(e, sourceId)\n\t\tconst handleSelectStart = (e: any) => this.handleSelectStart(e)\n\n\t\tnode.setAttribute('draggable', 'true')\n\t\tnode.addEventListener('dragstart', handleDragStart)\n\t\tnode.addEventListener('selectstart', handleSelectStart)\n\n\t\treturn (): void => {\n\t\t\tthis.sourceNodes.delete(sourceId)\n\t\t\tthis.sourceNodeOptions.delete(sourceId)\n\n\t\t\tnode.removeEventListener('dragstart', handleDragStart)\n\t\t\tnode.removeEventListener('selectstart', handleSelectStart)\n\t\t\tnode.setAttribute('draggable', 'false')\n\t\t}\n\t}\n\n\tpublic connectDropTarget(targetId: string, node: HTMLElement): Unsubscribe {\n\t\tconst handleDragEnter = (e: DragEvent) => this.handleDragEnter(e, targetId)\n\t\tconst handleDragOver = (e: DragEvent) => this.handleDragOver(e, targetId)\n\t\tconst handleDrop = (e: DragEvent) => this.handleDrop(e, targetId)\n\n\t\tnode.addEventListener('dragenter', handleDragEnter)\n\t\tnode.addEventListener('dragover', handleDragOver)\n\t\tnode.addEventListener('drop', handleDrop)\n\n\t\treturn (): void => {\n\t\t\tnode.removeEventListener('dragenter', handleDragEnter)\n\t\t\tnode.removeEventListener('dragover', handleDragOver)\n\t\t\tnode.removeEventListener('drop', handleDrop)\n\t\t}\n\t}\n\n\tprivate addEventListeners(target: Node) {\n\t\t// SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n\t\tif (!target.addEventListener) {\n\t\t\treturn\n\t\t}\n\t\ttarget.addEventListener(\n\t\t\t'dragstart',\n\t\t\tthis.handleTopDragStart as EventListener,\n\t\t)\n\t\ttarget.addEventListener('dragstart', this.handleTopDragStartCapture, true)\n\t\ttarget.addEventListener('dragend', this.handleTopDragEndCapture, true)\n\t\ttarget.addEventListener(\n\t\t\t'dragenter',\n\t\t\tthis.handleTopDragEnter as EventListener,\n\t\t)\n\t\ttarget.addEventListener(\n\t\t\t'dragenter',\n\t\t\tthis.handleTopDragEnterCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.addEventListener(\n\t\t\t'dragleave',\n\t\t\tthis.handleTopDragLeaveCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.addEventListener('dragover', this.handleTopDragOver as EventListener)\n\t\ttarget.addEventListener(\n\t\t\t'dragover',\n\t\t\tthis.handleTopDragOverCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.addEventListener('drop', this.handleTopDrop as EventListener)\n\t\ttarget.addEventListener(\n\t\t\t'drop',\n\t\t\tthis.handleTopDropCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t}\n\n\tprivate removeEventListeners(target: Node) {\n\t\t// SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n\t\tif (!target.removeEventListener) {\n\t\t\treturn\n\t\t}\n\t\ttarget.removeEventListener('dragstart', this.handleTopDragStart as any)\n\t\ttarget.removeEventListener(\n\t\t\t'dragstart',\n\t\t\tthis.handleTopDragStartCapture,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.removeEventListener('dragend', this.handleTopDragEndCapture, true)\n\t\ttarget.removeEventListener(\n\t\t\t'dragenter',\n\t\t\tthis.handleTopDragEnter as EventListener,\n\t\t)\n\t\ttarget.removeEventListener(\n\t\t\t'dragenter',\n\t\t\tthis.handleTopDragEnterCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.removeEventListener(\n\t\t\t'dragleave',\n\t\t\tthis.handleTopDragLeaveCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.removeEventListener(\n\t\t\t'dragover',\n\t\t\tthis.handleTopDragOver as EventListener,\n\t\t)\n\t\ttarget.removeEventListener(\n\t\t\t'dragover',\n\t\t\tthis.handleTopDragOverCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.removeEventListener('drop', this.handleTopDrop as EventListener)\n\t\ttarget.removeEventListener(\n\t\t\t'drop',\n\t\t\tthis.handleTopDropCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t}\n\n\tprivate getCurrentSourceNodeOptions() {\n\t\tconst sourceId = this.monitor.getSourceId() as string\n\t\tconst sourceNodeOptions = this.sourceNodeOptions.get(sourceId)\n\n\t\treturn {\n\t\t\tdropEffect: this.altKeyPressed ? 'copy' : 'move',\n\t\t\t...(sourceNodeOptions || {}),\n\t\t}\n\t}\n\n\tprivate getCurrentDropEffect() {\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\t// It makes more sense to default to 'copy' for native resources\n\t\t\treturn 'copy'\n\t\t}\n\n\t\treturn this.getCurrentSourceNodeOptions().dropEffect\n\t}\n\n\tprivate getCurrentSourcePreviewNodeOptions() {\n\t\tconst sourceId = this.monitor.getSourceId() as string\n\t\tconst sourcePreviewNodeOptions = this.sourcePreviewNodeOptions.get(sourceId)\n\n\t\treturn {\n\t\t\tanchorX: 0.5,\n\t\t\tanchorY: 0.5,\n\t\t\tcaptureDraggingState: false,\n\t\t\t...(sourcePreviewNodeOptions || {}),\n\t\t}\n\t}\n\n\tprivate getSourceClientOffset = (sourceId: string): XYCoord | null => {\n\t\tconst source = this.sourceNodes.get(sourceId)\n\t\treturn (source && getNodeClientOffset(source as HTMLElement)) || null\n\t}\n\n\tprivate isDraggingNativeItem() {\n\t\tconst itemType = this.monitor.getItemType()\n\t\treturn Object.keys(NativeTypes).some(\n\t\t\t(key: string) => (NativeTypes as any)[key] === itemType,\n\t\t)\n\t}\n\n\tprivate beginDragNativeItem(type: string, dataTransfer?: DataTransfer) {\n\t\tthis.clearCurrentDragSourceNode()\n\n\t\tthis.currentNativeSource = createNativeDragSource(type, dataTransfer)\n\t\tthis.currentNativeHandle = this.registry.addSource(\n\t\t\ttype,\n\t\t\tthis.currentNativeSource,\n\t\t)\n\t\tthis.actions.beginDrag([this.currentNativeHandle])\n\t}\n\n\tprivate endDragNativeItem = (): void => {\n\t\tif (!this.isDraggingNativeItem()) {\n\t\t\treturn\n\t\t}\n\n\t\tthis.actions.endDrag()\n\t\tif (this.currentNativeHandle) {\n\t\t\tthis.registry.removeSource(this.currentNativeHandle)\n\t\t}\n\t\tthis.currentNativeHandle = null\n\t\tthis.currentNativeSource = null\n\t}\n\n\tprivate isNodeInDocument = (node: Node | null | undefined): boolean => {\n\t\t// Check the node either in the main document or in the current context\n\t\treturn Boolean(\n\t\t\tnode &&\n\t\t\t\tthis.document &&\n\t\t\t\tthis.document.body &&\n\t\t\t\tthis.document.body.contains(node),\n\t\t)\n\t}\n\n\tprivate endDragIfSourceWasRemovedFromDOM = (): void => {\n\t\tconst node = this.currentDragSourceNode\n\t\tif (node == null || this.isNodeInDocument(node)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (this.clearCurrentDragSourceNode() && this.monitor.isDragging()) {\n\t\t\tthis.actions.endDrag()\n\t\t}\n\t\tthis.cancelHover()\n\t}\n\n\tprivate setCurrentDragSourceNode(node: Element | null) {\n\t\tthis.clearCurrentDragSourceNode()\n\t\tthis.currentDragSourceNode = node\n\n\t\t// A timeout of > 0 is necessary to resolve Firefox issue referenced\n\t\t// See:\n\t\t//   * https://github.com/react-dnd/react-dnd/pull/928\n\t\t//   * https://github.com/react-dnd/react-dnd/issues/869\n\t\tconst MOUSE_MOVE_TIMEOUT = 1000\n\n\t\t// Receiving a mouse event in the middle of a dragging operation\n\t\t// means it has ended and the drag source node disappeared from DOM,\n\t\t// so the browser didn't dispatch the dragend event.\n\t\t//\n\t\t// We need to wait before we start listening for mousemove events.\n\t\t// This is needed because the drag preview needs to be drawn or else it fires an 'mousemove' event\n\t\t// immediately in some browsers.\n\t\t//\n\t\t// See:\n\t\t//   * https://github.com/react-dnd/react-dnd/pull/928\n\t\t//   * https://github.com/react-dnd/react-dnd/issues/869\n\t\t//\n\t\tthis.mouseMoveTimeoutTimer = setTimeout(() => {\n\t\t\treturn this.rootElement?.addEventListener(\n\t\t\t\t'mousemove',\n\t\t\t\tthis.endDragIfSourceWasRemovedFromDOM,\n\t\t\t\ttrue,\n\t\t\t)\n\t\t}, MOUSE_MOVE_TIMEOUT) as any as number\n\t}\n\n\tprivate clearCurrentDragSourceNode() {\n\t\tif (this.currentDragSourceNode) {\n\t\t\tthis.currentDragSourceNode = null\n\n\t\t\tif (this.rootElement) {\n\t\t\t\tthis.window?.clearTimeout(this.mouseMoveTimeoutTimer || undefined)\n\t\t\t\tthis.rootElement.removeEventListener(\n\t\t\t\t\t'mousemove',\n\t\t\t\t\tthis.endDragIfSourceWasRemovedFromDOM,\n\t\t\t\t\ttrue,\n\t\t\t\t)\n\t\t\t}\n\n\t\t\tthis.mouseMoveTimeoutTimer = null\n\t\t\treturn true\n\t\t}\n\n\t\treturn false\n\t}\n\n\tprivate scheduleHover = (dragOverTargetIds: string[] | null) => {\n\t\tif (\n\t\t\tthis.hoverRafId === null &&\n\t\t\ttypeof requestAnimationFrame !== 'undefined'\n\t\t) {\n\t\t\tthis.hoverRafId = requestAnimationFrame(() => {\n\t\t\t\tif (this.monitor.isDragging()) {\n\t\t\t\t\tthis.actions.hover(dragOverTargetIds || [], {\n\t\t\t\t\t\tclientOffset: this.lastClientOffset,\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t\tthis.hoverRafId = null\n\t\t\t})\n\t\t}\n\t}\n\n\tprivate cancelHover = () => {\n\t\tif (\n\t\t\tthis.hoverRafId !== null &&\n\t\t\ttypeof cancelAnimationFrame !== 'undefined'\n\t\t) {\n\t\t\tcancelAnimationFrame(this.hoverRafId)\n\t\t\tthis.hoverRafId = null\n\t\t}\n\t}\n\n\tpublic handleTopDragStartCapture = (): void => {\n\t\tthis.clearCurrentDragSourceNode()\n\t\tthis.dragStartSourceIds = []\n\t}\n\n\tpublic handleDragStart(e: DragEvent, sourceId: string): void {\n\t\tif (e.defaultPrevented) {\n\t\t\treturn\n\t\t}\n\n\t\tif (!this.dragStartSourceIds) {\n\t\t\tthis.dragStartSourceIds = []\n\t\t}\n\t\tthis.dragStartSourceIds.unshift(sourceId)\n\t}\n\n\tpublic handleTopDragStart = (e: DragEvent): void => {\n\t\tif (e.defaultPrevented) {\n\t\t\treturn\n\t\t}\n\n\t\tconst { dragStartSourceIds } = this\n\t\tthis.dragStartSourceIds = null\n\n\t\tconst clientOffset = getEventClientOffset(e)\n\n\t\t// Avoid crashing if we missed a drop event or our previous drag died\n\t\tif (this.monitor.isDragging()) {\n\t\t\tthis.actions.endDrag()\n\t\t\tthis.cancelHover()\n\t\t}\n\n\t\t// Don't publish the source just yet (see why below)\n\t\tthis.actions.beginDrag(dragStartSourceIds || [], {\n\t\t\tpublishSource: false,\n\t\t\tgetSourceClientOffset: this.getSourceClientOffset,\n\t\t\tclientOffset,\n\t\t})\n\n\t\tconst { dataTransfer } = e\n\t\tconst nativeType = matchNativeItemType(dataTransfer)\n\n\t\tif (this.monitor.isDragging()) {\n\t\t\tif (dataTransfer && typeof dataTransfer.setDragImage === 'function') {\n\t\t\t\t// Use custom drag image if user specifies it.\n\t\t\t\t// If child drag source refuses drag but parent agrees,\n\t\t\t\t// use parent's node as drag image. Neither works in IE though.\n\t\t\t\tconst sourceId: string = this.monitor.getSourceId() as string\n\t\t\t\tconst sourceNode = this.sourceNodes.get(sourceId)\n\t\t\t\tconst dragPreview = this.sourcePreviewNodes.get(sourceId) || sourceNode\n\n\t\t\t\tif (dragPreview) {\n\t\t\t\t\tconst { anchorX, anchorY, offsetX, offsetY } =\n\t\t\t\t\t\tthis.getCurrentSourcePreviewNodeOptions()\n\t\t\t\t\tconst anchorPoint = { anchorX, anchorY }\n\t\t\t\t\tconst offsetPoint = { offsetX, offsetY }\n\t\t\t\t\tconst dragPreviewOffset = getDragPreviewOffset(\n\t\t\t\t\t\tsourceNode as HTMLElement,\n\t\t\t\t\t\tdragPreview as HTMLElement,\n\t\t\t\t\t\tclientOffset,\n\t\t\t\t\t\tanchorPoint,\n\t\t\t\t\t\toffsetPoint,\n\t\t\t\t\t)\n\n\t\t\t\t\tdataTransfer.setDragImage(\n\t\t\t\t\t\tdragPreview,\n\t\t\t\t\t\tdragPreviewOffset.x,\n\t\t\t\t\t\tdragPreviewOffset.y,\n\t\t\t\t\t)\n\t\t\t\t}\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\t// Firefox won't drag without setting data\n\t\t\t\tdataTransfer?.setData('application/json', {} as any)\n\t\t\t} catch (err) {\n\t\t\t\t// IE doesn't support MIME types in setData\n\t\t\t}\n\n\t\t\t// Store drag source node so we can check whether\n\t\t\t// it is removed from DOM and trigger endDrag manually.\n\t\t\tthis.setCurrentDragSourceNode(e.target as Element)\n\n\t\t\t// Now we are ready to publish the drag source.. or are we not?\n\t\t\tconst { captureDraggingState } = this.getCurrentSourcePreviewNodeOptions()\n\t\t\tif (!captureDraggingState) {\n\t\t\t\t// Usually we want to publish it in the next tick so that browser\n\t\t\t\t// is able to screenshot the current (not yet dragging) state.\n\t\t\t\t//\n\t\t\t\t// It also neatly avoids a situation where render() returns null\n\t\t\t\t// in the same tick for the source element, and browser freaks out.\n\t\t\t\tsetTimeout(() => this.actions.publishDragSource(), 0)\n\t\t\t} else {\n\t\t\t\t// In some cases the user may want to override this behavior, e.g.\n\t\t\t\t// to work around IE not supporting custom drag previews.\n\t\t\t\t//\n\t\t\t\t// When using a custom drag layer, the only way to prevent\n\t\t\t\t// the default drag preview from drawing in IE is to screenshot\n\t\t\t\t// the dragging state in which the node itself has zero opacity\n\t\t\t\t// and height. In this case, though, returning null from render()\n\t\t\t\t// will abruptly end the dragging, which is not obvious.\n\t\t\t\t//\n\t\t\t\t// This is the reason such behavior is strictly opt-in.\n\t\t\t\tthis.actions.publishDragSource()\n\t\t\t}\n\t\t} else if (nativeType) {\n\t\t\t// A native item (such as URL) dragged from inside the document\n\t\t\tthis.beginDragNativeItem(nativeType)\n\t\t} else if (\n\t\t\tdataTransfer &&\n\t\t\t!dataTransfer.types &&\n\t\t\t((e.target && !(e.target as Element).hasAttribute) ||\n\t\t\t\t!(e.target as Element).hasAttribute('draggable'))\n\t\t) {\n\t\t\t// Looks like a Safari bug: dataTransfer.types is null, but there was no draggable.\n\t\t\t// Just let it drag. It's a native type (URL or text) and will be picked up in\n\t\t\t// dragenter handler.\n\t\t\treturn\n\t\t} else {\n\t\t\t// If by this time no drag source reacted, tell browser not to drag.\n\t\t\te.preventDefault()\n\t\t}\n\t}\n\n\tpublic handleTopDragEndCapture = (): void => {\n\t\tif (this.clearCurrentDragSourceNode() && this.monitor.isDragging()) {\n\t\t\t// Firefox can dispatch this event in an infinite loop\n\t\t\t// if dragend handler does something like showing an alert.\n\t\t\t// Only proceed if we have not handled it already.\n\t\t\tthis.actions.endDrag()\n\t\t}\n\t\tthis.cancelHover()\n\t}\n\n\tpublic handleTopDragEnterCapture = (e: DragEvent): void => {\n\t\tthis.dragEnterTargetIds = []\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\tthis.currentNativeSource?.loadDataTransfer(e.dataTransfer)\n\t\t}\n\n\t\tconst isFirstEnter = this.enterLeaveCounter.enter(e.target)\n\t\tif (!isFirstEnter || this.monitor.isDragging()) {\n\t\t\treturn\n\t\t}\n\n\t\tconst { dataTransfer } = e\n\t\tconst nativeType = matchNativeItemType(dataTransfer)\n\n\t\tif (nativeType) {\n\t\t\t// A native item (such as file or URL) dragged from outside the document\n\t\t\tthis.beginDragNativeItem(nativeType, dataTransfer as DataTransfer)\n\t\t}\n\t}\n\n\tpublic handleDragEnter(_e: DragEvent, targetId: string): void {\n\t\tthis.dragEnterTargetIds.unshift(targetId)\n\t}\n\n\tpublic handleTopDragEnter = (e: DragEvent): void => {\n\t\tconst { dragEnterTargetIds } = this\n\t\tthis.dragEnterTargetIds = []\n\n\t\tif (!this.monitor.isDragging()) {\n\t\t\t// This is probably a native item type we don't understand.\n\t\t\treturn\n\t\t}\n\n\t\tthis.altKeyPressed = e.altKey\n\n\t\t// If the target changes position as the result of `dragenter`, `dragover` might still\n\t\t// get dispatched despite target being no longer there. The easy solution is to check\n\t\t// whether there actually is a target before firing `hover`.\n\t\tif (dragEnterTargetIds.length > 0) {\n\t\t\tthis.actions.hover(dragEnterTargetIds, {\n\t\t\t\tclientOffset: getEventClientOffset(e),\n\t\t\t})\n\t\t}\n\n\t\tconst canDrop = dragEnterTargetIds.some((targetId) =>\n\t\t\tthis.monitor.canDropOnTarget(targetId),\n\t\t)\n\n\t\tif (canDrop) {\n\t\t\t// IE requires this to fire dragover events\n\t\t\te.preventDefault()\n\t\t\tif (e.dataTransfer) {\n\t\t\t\te.dataTransfer.dropEffect = this.getCurrentDropEffect()\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic handleTopDragOverCapture = (e: DragEvent): void => {\n\t\tthis.dragOverTargetIds = []\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\tthis.currentNativeSource?.loadDataTransfer(e.dataTransfer)\n\t\t}\n\t}\n\n\tpublic handleDragOver(_e: DragEvent, targetId: string): void {\n\t\tif (this.dragOverTargetIds === null) {\n\t\t\tthis.dragOverTargetIds = []\n\t\t}\n\t\tthis.dragOverTargetIds.unshift(targetId)\n\t}\n\n\tpublic handleTopDragOver = (e: DragEvent): void => {\n\t\tconst { dragOverTargetIds } = this\n\t\tthis.dragOverTargetIds = []\n\n\t\tif (!this.monitor.isDragging()) {\n\t\t\t// This is probably a native item type we don't understand.\n\t\t\t// Prevent default \"drop and blow away the whole document\" action.\n\t\t\te.preventDefault()\n\t\t\tif (e.dataTransfer) {\n\t\t\t\te.dataTransfer.dropEffect = 'none'\n\t\t\t}\n\t\t\treturn\n\t\t}\n\n\t\tthis.altKeyPressed = e.altKey\n\t\tthis.lastClientOffset = getEventClientOffset(e)\n\n\t\tthis.scheduleHover(dragOverTargetIds)\n\n\t\tconst canDrop = (dragOverTargetIds || []).some((targetId) =>\n\t\t\tthis.monitor.canDropOnTarget(targetId),\n\t\t)\n\n\t\tif (canDrop) {\n\t\t\t// Show user-specified drop effect.\n\t\t\te.preventDefault()\n\t\t\tif (e.dataTransfer) {\n\t\t\t\te.dataTransfer.dropEffect = this.getCurrentDropEffect()\n\t\t\t}\n\t\t} else if (this.isDraggingNativeItem()) {\n\t\t\t// Don't show a nice cursor but still prevent default\n\t\t\t// \"drop and blow away the whole document\" action.\n\t\t\te.preventDefault()\n\t\t} else {\n\t\t\te.preventDefault()\n\t\t\tif (e.dataTransfer) {\n\t\t\t\te.dataTransfer.dropEffect = 'none'\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic handleTopDragLeaveCapture = (e: DragEvent): void => {\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\te.preventDefault()\n\t\t}\n\n\t\tconst isLastLeave = this.enterLeaveCounter.leave(e.target)\n\t\tif (!isLastLeave) {\n\t\t\treturn\n\t\t}\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\tsetTimeout(() => this.endDragNativeItem(), 0)\n\t\t}\n\t\tthis.cancelHover()\n\t}\n\n\tpublic handleTopDropCapture = (e: DragEvent): void => {\n\t\tthis.dropTargetIds = []\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\te.preventDefault()\n\t\t\tthis.currentNativeSource?.loadDataTransfer(e.dataTransfer)\n\t\t} else if (matchNativeItemType(e.dataTransfer)) {\n\t\t\t// Dragging some elements, like <a> and <img> may still behave like a native drag event,\n\t\t\t// even if the current drag event matches a user-defined type.\n\t\t\t// Stop the default behavior when we're not expecting a native item to be dropped.\n\n\t\t\te.preventDefault()\n\t\t}\n\n\t\tthis.enterLeaveCounter.reset()\n\t}\n\n\tpublic handleDrop(_e: DragEvent, targetId: string): void {\n\t\tthis.dropTargetIds.unshift(targetId)\n\t}\n\n\tpublic handleTopDrop = (e: DragEvent): void => {\n\t\tconst { dropTargetIds } = this\n\t\tthis.dropTargetIds = []\n\n\t\tthis.actions.hover(dropTargetIds, {\n\t\t\tclientOffset: getEventClientOffset(e),\n\t\t})\n\t\tthis.actions.drop({ dropEffect: this.getCurrentDropEffect() })\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\tthis.endDragNativeItem()\n\t\t} else if (this.monitor.isDragging()) {\n\t\t\tthis.actions.endDrag()\n\t\t}\n\t\tthis.cancelHover()\n\t}\n\n\tpublic handleSelectStart = (e: DragEvent): void => {\n\t\tconst target = e.target as HTMLElement & { dragDrop: () => void }\n\n\t\t// Only IE requires us to explicitly say\n\t\t// we want drag drop operation to start\n\t\tif (typeof target.dragDrop !== 'function') {\n\t\t\treturn\n\t\t}\n\n\t\t// Inputs and textareas should be selectable\n\t\tif (\n\t\t\ttarget.tagName === 'INPUT' ||\n\t\t\ttarget.tagName === 'SELECT' ||\n\t\t\ttarget.tagName === 'TEXTAREA' ||\n\t\t\ttarget.isContentEditable\n\t\t) {\n\t\t\treturn\n\t\t}\n\n\t\t// For other targets, ask IE\n\t\t// to enable drag and drop\n\t\te.preventDefault()\n\t\ttarget.dragDrop()\n\t}\n}\n", "let emptyImage: HTMLImageElement | undefined\n\nexport function getEmptyImage(): HTMLImageElement {\n\tif (!emptyImage) {\n\t\temptyImage = new Image()\n\t\temptyImage.src =\n\t\t\t'data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=='\n\t}\n\n\treturn emptyImage\n}\n", "import type { BackendFactory, DragDropManager } from 'dnd-core'\n\nimport { HTML5BackendImpl } from './HTML5BackendImpl.js'\nimport type { HTML5BackendContext, HTML5BackendOptions } from './types.js'\nexport { getEmptyImage } from './getEmptyImage.js'\nexport * as NativeTypes from './NativeTypes.js'\nexport type { HTML5BackendContext, HTML5BackendOptions } from './types.js'\n\nexport const HTML5Backend: BackendFactory = function createBackend(\n\tmanager: DragDropManager,\n\tcontext?: HTML5BackendContext,\n\toptions?: HTML5BackendOptions,\n): HTML5BackendImpl {\n\treturn new HTML5BackendImpl(manager, context, options)\n}\n"], "mappings": ";;;;;AAEO,SAASA,QAAWC,IAAsB;AAChD,MAAIC,SAAmB;AACvB,QAAMC,WAAW,MAAM;AACtB,QAAID,UAAU,MAAM;AACnBA,eAASD,GAAE;;AAEZ,WAAOC;;AAER,SAAOC;;AAMD,SAASC,QAAWC,OAAYC,MAAS;AAC/C,SAAOD,MAAME;IAAO,CAACC,MAAMA,MAAMF;EAAI;;AAG/B,SAASG,MAAiCC,QAAaC,QAAa;AAC1E,QAAMC,MAAM,oBAAIC,IAAG;AACnB,QAAMC,aAAa,CAACR,SAAYM,IAAIG,IAAIT,IAAI;AAC5CI,SAAOM,QAAQF,UAAU;AACzBH,SAAOK,QAAQF,UAAU;AAEzB,QAAMZ,SAAc,CAAA;AACpBU,MAAII;IAAQ,CAACC,QAAQf,OAAOgB,KAAKD,GAAG;EAAC;AACrC,SAAOf;;;;ACxBD,IAAMiB,oBAAN,MAAuB;EAQtBC,MAAMC,cAA2C;AACvD,UAAMC,iBAAiB,KAAKC,QAAQC;AAEpC,UAAMC,gBAAgB,CAACC,SACtB,KAAKC,iBAAiBD,IAAI,MACzB,CAACA,KAAKE,YAAYF,KAAKE,SAASP,YAAY;AAE9C,SAAKE,UAAUM,MAAM,KAAKN,QAAQO,OAAOL,aAAa,GAAG;MAACJ;KAAa;AAEvE,WAAOC,mBAAmB,KAAK,KAAKC,QAAQC,SAAS;;EAG/CO,MAAMC,aAA0C;AACtD,UAAMV,iBAAiB,KAAKC,QAAQC;AAEpC,SAAKD,UAAUU,QACd,KAAKV,QAAQO,OAAO,KAAKH,gBAAgB,GACzCK,WAAW;AAGZ,WAAOV,iBAAiB,KAAK,KAAKC,QAAQC,WAAW;;EAG/CU,QAAc;AACpB,SAAKX,UAAU,CAAA;;EA5BhB,YAAmBI,kBAAiC;AAHpD,SAAQJ,UAAiB,CAAA;AAIxB,SAAKI,mBAAmBA;;;;;ACLnB,IAAMQ,mBAAN,MAAsB;EAUpBC,8BAA8B;AACrCC,WAAOC,KAAK,KAAKC,OAAOC,gBAAgB,EAAEC,QAAQ,CAACC,aAAa;AAC/DL,aAAOM,eAAe,KAAKC,MAAMF,UAAU;QAC1CG,cAAc;QACdC,YAAY;QACZC,MAAM;AAELC,kBAAQC,KACP,kCAAkCP,QAAQ,yBAAyB;AAEpE,iBAAO;;OAER;KACD;;EAGKQ,iBAAiBC,cAAqD;AAC5E,QAAIA,cAAc;AACjB,YAAMC,gBAAuC,CAAA;AAC7Cf,aAAOC,KAAK,KAAKC,OAAOC,gBAAgB,EAAEC,QAAQ,CAACC,aAAa;AAC/D,cAAMW,aAAa,KAAKd,OAAOC,iBAAiBE,QAAQ;AACxD,YAAIW,cAAc,MAAM;AACvBD,wBAAcV,QAAQ,IAAI;YACzBY,OAAOD,WAAWF,cAAc,KAAKZ,OAAOgB,YAAY;YACxDV,cAAc;YACdC,YAAY;;;OAGd;AACDT,aAAOmB,iBAAiB,KAAKZ,MAAMQ,aAAa;;;EAI3CK,UAAmB;AACzB,WAAO;;EAGDC,YAAiB;AACvB,WAAO,KAAKd;;EAGNe,WAAWC,SAA0BC,QAAyB;AACpE,WAAOA,WAAWD,QAAQE,YAAW;;EAG/BC,UAAgB;;EAnDvB,YAAmBxB,QAA0B;AAC5C,SAAKA,SAASA;AACd,SAAKK,OAAO,CAAA;AACZ,SAAKR,4BAA2B;;;;;ACXlC;;;;;;;AAAO,IAAM4B,OAAO;AACb,IAAMC,MAAM;AACZ,IAAMC,OAAO;AACb,IAAMC,OAAO;;;ACHb,SAASC,wBACfC,cACAC,YACAC,cACS;AACT,QAAMC,SAASF,WAAWG;IACzB,CAACC,aAAaC,cAAcD,eAAeL,aAAaO,QAAQD,SAAS;IACzE;EAAE;AAGH,SAAOH,UAAU,OAAOA,SAASD;;;;ACK3B,IAAMM,oBAET;EACH,CAAaC,IAAI,GAAG;IACnBC,kBAAkB;MACjBC,OAAO,CAACC,iBACPC,MAAMC,UAAUC,MAAMC,KAAKJ,aAAaD,KAAK;MAC9CM,OAAO,CAACL,iBACPA,aAAaK;MACdL,cAAc,CAACA,iBAA6CA;;IAE7DM,cAAc;MAAC;;;EAEhB,CAAaC,IAAI,GAAG;IACnBT,kBAAkB;MACjBU,MAAM,CAACR,cAA4BM,iBAClCG,wBAAwBT,cAAcM,cAAc,EAAE;MACvDN,cAAc,CAACA,iBAA6CA;;IAE7DM,cAAc;MAAC;MAAQ;;;EAExB,CAAaI,GAAG,GAAG;IAClBZ,kBAAkB;MACjBa,MAAM,CAACX,cAA4BM,iBAClCG,wBAAwBT,cAAcM,cAAc,EAAE,EAAEM,MAAM,IAAI;MACnEZ,cAAc,CAACA,iBAA6CA;;IAE7DM,cAAc;MAAC;MAAO;;;EAEvB,CAAaO,IAAI,GAAG;IACnBf,kBAAkB;MACjBgB,MAAM,CAACd,cAA4BM,iBAClCG,wBAAwBT,cAAcM,cAAc,EAAE;MACvDN,cAAc,CAACA,iBAA6CA;;IAE7DM,cAAc;MAAC;MAAQ;;;;;;AC/ClB,SAASS,uBACfC,MACAC,cACmB;AACnB,QAAMC,SAASC,kBAAkBH,IAAI;AACrC,MAAI,CAACE,QAAQ;AACZ,UAAM,IAAIE,MAAM,eAAeJ,IAAI,uBAAuB;;AAE3D,QAAMK,SAAS,IAAIC,iBAAiBJ,MAAM;AAC1CG,SAAOE,iBAAiBN,YAAY;AACpC,SAAOI;;AAGD,SAASG,oBACfP,cACgB;AAChB,MAAI,CAACA,cAAc;AAClB,WAAO;;AAGR,QAAMQ,oBAAoBC,MAAMC,UAAUC,MAAMC,KAAKZ,aAAaa,SAAS,CAAA,CAAE;AAC7E,SACCC,OAAOC,KAAKb,iBAAiB,EAAEc,OAAO,CAACC,mBAAmB;AACzD,UAAMC,aAAahB,kBAAkBe,cAAc;AACnD,QAAI,EAACC,eAAU,QAAVA,eAAU,SAAVA,SAAAA,WAAYC,eAAc;AAC9B,aAAO;;AAER,WAAOD,WAAWC,aAAaC;MAC9B,CAACC,MAAMb,kBAAkBc,QAAQD,CAAC,IAAI;IAAE;GAEzC,EAAE,CAAC,KAAK;;;;ACxBJ,IAAME,YAAuBC;EAAQ,MAC3C,WAAWC,KAAKC,UAAUC,SAAS;AAAC;AAE9B,IAAMC,WAAsBJ;EAAQ,MAAMK,QAAQC,OAAOC,MAAM;AAAC;;;ACZhE,IAAMC,uBAAN,MAA0B;EAqEzBC,YAAYC,GAAmB;AACrC,UAAM,EAAEC,IAAIC,IAAIC,KAAKC,KAAKC,IAAG,IAAK;AAGlC,QAAIC,IAAIL,GAAGM,SAAS;AACpB,QAAIP,MAAMC,GAAGK,CAAC,GAAG;AAChB,aAAOJ,GAAGI,CAAC;;AAIZ,QAAIE,MAAM;AACV,QAAIC,OAAOJ,IAAIE,SAAS;AACxB,QAAIG;AACJ,WAAOF,OAAOC,MAAM;AACnBC,YAAMC,KAAKC,MAAM,OAAOJ,MAAMC,KAAK;AACnC,YAAMI,QAAQZ,GAAGS,GAAG;AACpB,UAAIG,QAAQb,GAAG;AACdQ,cAAME,MAAM;iBACFG,QAAQb,GAAG;AACrBS,eAAOC,MAAM;aACP;AACN,eAAOR,GAAGQ,GAAG;;;AAGfJ,QAAIK,KAAKG,IAAI,GAAGL,IAAI;AAGpB,UAAMM,OAAOf,IAAIC,GAAGK,CAAC;AACrB,UAAMU,SAASD,OAAOA;AACtB,WAAOb,GAAGI,CAAC,IAAIH,IAAIG,CAAC,IAAIS,OAAOX,IAAIE,CAAC,IAAIU,SAASX,IAAIC,CAAC,IAAIS,OAAOC;;EA3FlE,YAAmBf,IAAcC,IAAc;AAC9C,UAAM,EAAEK,OAAM,IAAKN;AAGnB,UAAMgB,UAAU,CAAA;AAChB,aAASX,IAAI,GAAGA,IAAIC,QAAQD,KAAK;AAChCW,cAAQC,KAAKZ,CAAC;;AAEfW,YAAQE;MAAK,CAACC,GAAGC,MAAQpB,GAAGmB,CAAC,IAAgBnB,GAAGoB,CAAC,IAAe,KAAK;IAAE;AAGvE,UAAMC,MAAM,CAAA;AACZ,UAAMC,MAAM,CAAA;AACZ,UAAMC,KAAK,CAAA;AACX,QAAIC;AACJ,QAAIC;AACJ,aAASpB,KAAI,GAAGA,KAAIC,SAAS,GAAGD,MAAK;AACpCmB,WAAMxB,GAAGK,KAAI,CAAC,IAAgBL,GAAGK,EAAC;AAClCoB,WAAMxB,GAAGI,KAAI,CAAC,IAAgBJ,GAAGI,EAAC;AAClCiB,UAAIL,KAAKO,EAAE;AACXH,UAAIJ,KAAKQ,EAAE;AACXF,SAAGN,KAAKQ,KAAKD,EAAE;;AAIhB,UAAMtB,MAAM;MAACqB,GAAG,CAAC;;AACjB,aAASlB,KAAI,GAAGA,KAAIiB,IAAIhB,SAAS,GAAGD,MAAK;AACxC,YAAMqB,KAAKH,GAAGlB,EAAC;AACf,YAAMsB,QAAQJ,GAAGlB,KAAI,CAAC;AACtB,UAAIqB,KAAKC,SAAS,GAAG;AACpBzB,YAAIe,KAAK,CAAC;aACJ;AACNO,aAAKF,IAAIjB,EAAC;AACV,cAAMuB,SAASN,IAAIjB,KAAI,CAAC;AACxB,cAAMwB,SAASL,KAAKI;AACpB1B,YAAIe,KACF,IAAIY,WAAYA,SAASD,UAAUF,MAAMG,SAASL,MAAMG,MAAM;;;AAIlEzB,QAAIe,KAAKM,GAAGA,GAAGjB,SAAS,CAAC,CAAC;AAG1B,UAAMH,MAAM,CAAA;AACZ,UAAMC,MAAM,CAAA;AACZ,QAAI0B;AACJ,aAASzB,KAAI,GAAGA,KAAIH,IAAII,SAAS,GAAGD,MAAK;AACxCyB,UAAIP,GAAGlB,EAAC;AACR,YAAM0B,KAAK7B,IAAIG,EAAC;AAChB,YAAM2B,QAAQ,IAAKV,IAAIjB,EAAC;AACxB,YAAMwB,SAASE,KAAM7B,IAAIG,KAAI,CAAC,IAAeyB,IAAIA;AACjD3B,UAAIc,MAAMa,IAAIC,KAAKF,UAAUG,KAAK;AAClC5B,UAAIa,KAAKY,SAASG,QAAQA,KAAK;;AAGhC,SAAKhC,KAAKA;AACV,SAAKC,KAAKA;AACV,SAAKC,MAAMA;AACX,SAAKC,MAAMA;AACX,SAAKC,MAAMA;;;;;AC7Db,IAAM6B,eAAe;AAEd,SAASC,oBAAoBC,MAA4B;AAC/D,QAAMC,KAAKD,KAAKE,aAAaJ,eAAeE,OAAOA,KAAKG;AAExD,MAAI,CAACF,IAAI;AACR,WAAO;;AAGR,QAAM,EAAEG,KAAKC,KAAI,IAAMJ,GAAmBK,sBAAqB;AAC/D,SAAO;IAAEC,GAAGF;IAAMG,GAAGJ;;;AAGf,SAASK,qBAAqBC,GAAwB;AAC5D,SAAO;IACNH,GAAGG,EAAEC;IACLH,GAAGE,EAAEE;;;AAIP,SAASC,YAAYb,MAAW;MAGbc;AAFlB,SACCd,KAAKe,aAAa,UACjBC,UAAS,KAAM,GAACF,MAAAA,SAASG,qBAAe,QAAxBH,QAAwB,SAAxBA,SAAAA,IAA0BI,SAASlB,IAAI;;AAI1D,SAASmB,mBACRC,SACAC,aACAC,aACAC,cACC;AACD,MAAIC,mBAAmBJ,UAAUC,YAAYI,QAAQH;AACrD,MAAII,oBAAoBN,UAAUC,YAAYM,SAASJ;AAGvD,MAAIK,SAAQ,KAAMR,SAAS;AAC1BM,yBAAqBG,OAAOC;AAC5BN,wBAAoBK,OAAOC;;AAE5B,SAAO;IAAEN;IAAkBE;;;AAGrB,SAASK,qBACfC,YACAX,aACAY,cACAC,aACAC,aACU;AAGV,QAAMf,UAAUP,YAAYQ,WAAW;AACvC,QAAMe,kBAAkBhB,UAAUY,aAAaX;AAC/C,QAAMgB,kCAAkCtC,oBACvCqC,eAAe;AAEhB,QAAME,wBAAwB;IAC7B/B,GAAG0B,aAAa1B,IAAI8B,gCAAgC9B;IACpDC,GAAGyB,aAAazB,IAAI6B,gCAAgC7B;;AAErD,QAAM,EAAE+B,aAAajB,aAAakB,cAAcjB,aAAY,IAAKS;AACjE,QAAM,EAAES,SAASC,QAAO,IAAKR;AAC7B,QAAM,EAAEV,kBAAkBE,kBAAiB,IAAKP,mBAC/CC,SACAC,aACAC,aACAC,YAAY;AAGb,QAAMoB,mBAAmB,MAAM;AAC9B,UAAMC,eAAe,IAAIC,qBACxB;MAAC;MAAG;MAAK;OACT;;MAECP,sBAAsB9B;;MAErB8B,sBAAsB9B,IAAIe,eAAgBG;;MAE3CY,sBAAsB9B,IAAIkB,oBAAoBH;KAC9C;AAEF,QAAIf,IAAIoC,aAAaE,YAAYJ,OAAO;AAExC,QAAId,SAAQ,KAAMR,SAAS;AAE1BZ,YAAMqB,OAAOC,mBAAmB,KAAKJ;;AAEtC,WAAOlB;;AAGR,QAAMuC,mBAAmB,MAAM;AAG9B,UAAMC,eAAe,IAAIH,qBACxB;MAAC;MAAG;MAAK;OACT;;MAECP,sBAAsB/B;;MAErB+B,sBAAsB/B,IAAIe,cAAeE;;MAE1Cc,sBAAsB/B,IAAIiB,mBAAmBF;KAC7C;AAEF,WAAO0B,aAAaF,YAAYL,OAAO;;AAIxC,QAAM,EAAEQ,SAASC,QAAO,IAAKf;AAC7B,QAAMgB,kBAAkBF,YAAY,KAAKA;AACzC,QAAMG,kBAAkBF,YAAY,KAAKA;AACzC,SAAO;IACN3C,GAAG4C,kBAAkBF,UAAUF,iBAAgB;IAC/CvC,GAAG4C,kBAAkBF,UAAUP,iBAAgB;;;;;ACtH1C,IAAMU,gBAAN,MAAmB;EAazB,IAAWC,SAA6B;AACvC,QAAI,KAAKC,eAAe;AACvB,aAAO,KAAKA;eACF,OAAOD,WAAW,aAAa;AACzC,aAAOA;;AAER,WAAOE;;EAGR,IAAWC,WAAiC;QACvC;AAAJ,SAAI,MAAA,KAAKF,mBAAa,QAAlB,QAAkB,SAAlB,SAAA,IAAoBE,UAAU;AACjC,aAAO,KAAKF,cAAcE;eAChB,KAAKH,QAAQ;AACvB,aAAO,KAAKA,OAAOG;WACb;AACN,aAAOD;;;EAIT,IAAWE,cAAgC;QACnC;AAAP,aAAO,MAAA,KAAKC,iBAAW,QAAhB,QAAgB,SAAhB,SAAA,IAAkBD,gBAAe,KAAKJ;;EA5B9C,YACCC,eACAK,SACC;AAPF,SAAOC,gBAAiC;AAQvC,SAAKN,gBAAgBA;AACrB,SAAKI,cAAcC;;;;;ACZrB,SAAA,gBAAA,KAAA,KAAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BO,IAAME,mBAAN,MAAsB;;;;EA6CrBC,UAAkC;QAMnB,KAGD;AARpB,WAAO;MACNC,oBAAoB,KAAKA,mBAAmBC;MAC5CC,0BAA0B,KAAKA,yBAAyBD;MACxDE,mBAAmB,KAAKA,kBAAkBF;MAC1CG,aAAa,KAAKA,YAAYH;MAC9BI,sBAAoB,MAAA,KAAKA,wBAAkB,QAAvB,QAAuB,SAAvB,SAAA,IAAyBC,WAAU;MACvDC,eAAe,KAAKA,cAAcD;MAClCE,oBAAoB,KAAKA,mBAAmBF;MAC5CG,qBAAmB,OAAA,KAAKA,uBAAiB,QAAtB,SAAsB,SAAtB,SAAA,KAAwBH,WAAU;;;;EAKvD,IAAWI,SAA6B;AACvC,WAAO,KAAKC,QAAQD;;EAErB,IAAWE,WAAiC;AAC3C,WAAO,KAAKD,QAAQC;;;;;EAKrB,IAAYC,cAAgC;AAC3C,WAAO,KAAKF,QAAQE;;EAGdC,QAAc;AACpB,UAAMC,OAAO,KAAKF;AAClB,QAAIE,SAASC,QAAW;AACvB;;AAGD,QAAID,KAAKE,0BAA0B;AAClC,YAAM,IAAIC,MAAM,kDAAkD;;AAEnEH,SAAKE,2BAA2B;AAChC,SAAKE,kBAAkBJ,IAAI;;EAGrBK,WAAiB;AACvB,UAAML,OAAO,KAAKF;AAClB,QAAIE,SAASC,QAAW;AACvB;;AAGDD,SAAKE,2BAA2B;AAChC,SAAKI,qBAAqB,KAAKR,WAAW;AAC1C,SAAKS,2BAA0B;AAC/B,QAAI,KAAKC,qBAAqB;UAC7B;AAAA,OAAA,MAAA,KAAKb,YAAM,QAAX,QAAW,SAAX,SAAA,IAAac,qBAAqB,KAAKD,mBAAmB;;;EAIrDE,mBACNC,UACAC,MACAhB,SACc;AACd,SAAKT,yBAAyB0B,IAAIF,UAAUf,OAAO;AACnD,SAAKX,mBAAmB4B,IAAIF,UAAUC,IAAI;AAE1C,WAAO,MAAY;AAClB,WAAK3B,mBAAmB6B,OAAOH,QAAQ;AACvC,WAAKxB,yBAAyB2B,OAAOH,QAAQ;;;EAIxCI,kBACNJ,UACAC,MACAhB,SACc;AACd,SAAKP,YAAYwB,IAAIF,UAAUC,IAAI;AACnC,SAAKxB,kBAAkByB,IAAIF,UAAUf,OAAO;AAE5C,UAAMoB,kBAAkB,CAACC,MAAW,KAAKD,gBAAgBC,GAAGN,QAAQ;AACpE,UAAMO,oBAAoB,CAACD,MAAW,KAAKC,kBAAkBD,CAAC;AAE9DL,SAAKO,aAAa,aAAa,MAAM;AACrCP,SAAKQ,iBAAiB,aAAaJ,eAAe;AAClDJ,SAAKQ,iBAAiB,eAAeF,iBAAiB;AAEtD,WAAO,MAAY;AAClB,WAAK7B,YAAYyB,OAAOH,QAAQ;AAChC,WAAKvB,kBAAkB0B,OAAOH,QAAQ;AAEtCC,WAAKS,oBAAoB,aAAaL,eAAe;AACrDJ,WAAKS,oBAAoB,eAAeH,iBAAiB;AACzDN,WAAKO,aAAa,aAAa,OAAO;;;EAIjCG,kBAAkBC,UAAkBX,MAAgC;AAC1E,UAAMY,kBAAkB,CAACP,MAAiB,KAAKO,gBAAgBP,GAAGM,QAAQ;AAC1E,UAAME,iBAAiB,CAACR,MAAiB,KAAKQ,eAAeR,GAAGM,QAAQ;AACxE,UAAMG,aAAa,CAACT,MAAiB,KAAKS,WAAWT,GAAGM,QAAQ;AAEhEX,SAAKQ,iBAAiB,aAAaI,eAAe;AAClDZ,SAAKQ,iBAAiB,YAAYK,cAAc;AAChDb,SAAKQ,iBAAiB,QAAQM,UAAU;AAExC,WAAO,MAAY;AAClBd,WAAKS,oBAAoB,aAAaG,eAAe;AACrDZ,WAAKS,oBAAoB,YAAYI,cAAc;AACnDb,WAAKS,oBAAoB,QAAQK,UAAU;;;EAIrCtB,kBAAkBuB,QAAc;AAEvC,QAAI,CAACA,OAAOP,kBAAkB;AAC7B;;AAEDO,WAAOP,iBACN,aACA,KAAKQ,kBAAkB;AAExBD,WAAOP,iBAAiB,aAAa,KAAKS,2BAA2B,IAAI;AACzEF,WAAOP,iBAAiB,WAAW,KAAKU,yBAAyB,IAAI;AACrEH,WAAOP,iBACN,aACA,KAAKW,kBAAkB;AAExBJ,WAAOP,iBACN,aACA,KAAKY,2BACL,IAAI;AAELL,WAAOP,iBACN,aACA,KAAKa,2BACL,IAAI;AAELN,WAAOP,iBAAiB,YAAY,KAAKc,iBAAiB;AAC1DP,WAAOP,iBACN,YACA,KAAKe,0BACL,IAAI;AAELR,WAAOP,iBAAiB,QAAQ,KAAKgB,aAAa;AAClDT,WAAOP,iBACN,QACA,KAAKiB,sBACL,IAAI;;EAIE/B,qBAAqBqB,QAAc;AAE1C,QAAI,CAACA,OAAON,qBAAqB;AAChC;;AAEDM,WAAON,oBAAoB,aAAa,KAAKO,kBAAkB;AAC/DD,WAAON,oBACN,aACA,KAAKQ,2BACL,IAAI;AAELF,WAAON,oBAAoB,WAAW,KAAKS,yBAAyB,IAAI;AACxEH,WAAON,oBACN,aACA,KAAKU,kBAAkB;AAExBJ,WAAON,oBACN,aACA,KAAKW,2BACL,IAAI;AAELL,WAAON,oBACN,aACA,KAAKY,2BACL,IAAI;AAELN,WAAON,oBACN,YACA,KAAKa,iBAAiB;AAEvBP,WAAON,oBACN,YACA,KAAKc,0BACL,IAAI;AAELR,WAAON,oBAAoB,QAAQ,KAAKe,aAAa;AACrDT,WAAON,oBACN,QACA,KAAKgB,sBACL,IAAI;;EAIEC,8BAA8B;AACrC,UAAM3B,WAAW,KAAK4B,QAAQC,YAAW;AACzC,UAAMpD,oBAAoB,KAAKA,kBAAkBqD,IAAI9B,QAAQ;AAE7D,WAAO,cAAA;MACN+B,YAAY,KAAKC,gBAAgB,SAAS;OACtCvD,qBAAqB,CAAA,CAAE;;EAIrBwD,uBAAuB;AAC9B,QAAI,KAAKC,qBAAoB,GAAI;AAEhC,aAAO;;AAGR,WAAO,KAAKP,4BAA2B,EAAGI;;EAGnCI,qCAAqC;AAC5C,UAAMnC,WAAW,KAAK4B,QAAQC,YAAW;AACzC,UAAMrD,2BAA2B,KAAKA,yBAAyBsD,IAAI9B,QAAQ;AAE3E,WAAO,cAAA;MACNoC,SAAS;MACTC,SAAS;MACTC,sBAAsB;OAClB9D,4BAA4B,CAAA,CAAE;;EAS5B0D,uBAAuB;AAC9B,UAAMK,WAAW,KAAKX,QAAQY,YAAW;AACzC,WAAOC,OAAOC,KAAKC,mBAAW,EAAEC;MAC/B,CAACC,QAAiBF,oBAAoBE,GAAG,MAAMN;IAAQ;;EAIjDO,oBAAoBC,MAAcC,cAA6B;AACtE,SAAKpD,2BAA0B;AAE/B,SAAKqD,sBAAsBC,uBAAuBH,MAAMC,YAAY;AACpE,SAAKG,sBAAsB,KAAKC,SAASC,UACxCN,MACA,KAAKE,mBAAmB;AAEzB,SAAKK,QAAQC,UAAU;MAAC,KAAKJ;KAAoB;;EAsC1CK,yBAAyBvD,MAAsB;AACtD,SAAKL,2BAA0B;AAC/B,SAAK6D,wBAAwBxD;AAM7B,UAAMyD,qBAAqB;AAc3B,SAAKC,wBAAwBC,WAAW,MAAM;UACtC;AAAP,cAAO,MAAA,KAAKzE,iBAAW,QAAhB,QAAgB,SAAhB,SAAA,IAAkBsB,iBACxB,aACA,KAAKoD,kCACL,IAAI;OAEHH,kBAAkB;;EAGd9D,6BAA6B;AACpC,QAAI,KAAK6D,uBAAuB;AAC/B,WAAKA,wBAAwB;AAE7B,UAAI,KAAKtE,aAAa;YACrB;AAAA,SAAA,MAAA,KAAKH,YAAM,QAAX,QAAW,SAAX,SAAA,IAAa8E,aAAa,KAAKH,yBAAyBrE,MAAS;AACjE,aAAKH,YAAYuB,oBAChB,aACA,KAAKmD,kCACL,IAAI;;AAIN,WAAKF,wBAAwB;AAC7B,aAAO;;AAGR,WAAO;;EAmCDtD,gBAAgBC,GAAcN,UAAwB;AAC5D,QAAIM,EAAEyD,kBAAkB;AACvB;;AAGD,QAAI,CAAC,KAAKpF,oBAAoB;AAC7B,WAAKA,qBAAqB,CAAA;;AAE3B,SAAKA,mBAAmBqF,QAAQhE,QAAQ;;EA8IlCa,gBAAgBoD,IAAerD,UAAwB;AAC7D,SAAK9B,mBAAmBkF,QAAQpD,QAAQ;;EA4ClCE,eAAemD,IAAerD,UAAwB;AAC5D,QAAI,KAAK7B,sBAAsB,MAAM;AACpC,WAAKA,oBAAoB,CAAA;;AAE1B,SAAKA,kBAAkBiF,QAAQpD,QAAQ;;EA6EjCG,WAAWkD,IAAerD,UAAwB;AACxD,SAAK/B,cAAcmF,QAAQpD,QAAQ;;EA/oBpC,YACCsD,SACAC,eACAlF,SACC;AAvBF,SAAQX,qBAA2C,oBAAI8F,IAAG;AAC1D,SAAQ5F,2BAA6C,oBAAI4F,IAAG;AAC5D,SAAQ1F,cAAoC,oBAAI0F,IAAG;AACnD,SAAQ3F,oBAAsC,oBAAI2F,IAAG;AAErD,SAAQzF,qBAAsC;AAC9C,SAAQE,gBAA0B,CAAA;AAClC,SAAQC,qBAA+B,CAAA;AACvC,SAAQmE,sBAA+C;AACvD,SAAQE,sBAAyC;AACjD,SAAQM,wBAAwC;AAChD,SAAQzB,gBAAgB;AACxB,SAAQ2B,wBAAuC;AAC/C,SAAQ9D,sBAAqC;AAC7C,SAAQd,oBAAqC;AAE7C,SAAQsF,mBAAmC;AAC3C,SAAQC,aAA4B;AA+OpC,SAAQC,wBAAwB,CAACvE,aAAqC;AACrE,YAAMwE,SAAS,KAAK9F,YAAYoD,IAAI9B,QAAQ;AAC5C,aAAQwE,UAAUC,oBAAoBD,MAAM,KAAqB;;AAqBlE,SAAQE,oBAAoB,MAAY;AACvC,UAAI,CAAC,KAAKxC,qBAAoB,GAAI;AACjC;;AAGD,WAAKoB,QAAQqB,QAAO;AACpB,UAAI,KAAKxB,qBAAqB;AAC7B,aAAKC,SAASwB,aAAa,KAAKzB,mBAAmB;;AAEpD,WAAKA,sBAAsB;AAC3B,WAAKF,sBAAsB;;AAG5B,SAAQ4B,mBAAmB,CAAC5E,SAA2C;AAEtE,aAAO6E,QACN7E,QACC,KAAKf,YACL,KAAKA,SAAS6F,QACd,KAAK7F,SAAS6F,KAAKC,SAAS/E,IAAI,CAAC;;AAIpC,SAAQ4D,mCAAmC,MAAY;AACtD,YAAM5D,OAAO,KAAKwD;AAClB,UAAIxD,QAAQ,QAAQ,KAAK4E,iBAAiB5E,IAAI,GAAG;AAChD;;AAGD,UAAI,KAAKL,2BAA0B,KAAM,KAAKgC,QAAQqD,WAAU,GAAI;AACnE,aAAK3B,QAAQqB,QAAO;;AAErB,WAAKO,YAAW;;AAsDjB,SAAQC,gBAAgB,CAACpG,sBAAuC;AAC/D,UACC,KAAKuF,eAAe,QACpB,OAAOc,0BAA0B,aAChC;AACD,aAAKd,aAAac,sBAAsB,MAAM;AAC7C,cAAI,KAAKxD,QAAQqD,WAAU,GAAI;AAC9B,iBAAK3B,QAAQ+B,MAAMtG,qBAAqB,CAAA,GAAI;cAC3CuG,cAAc,KAAKjB;aACnB;;AAGF,eAAKC,aAAa;SAClB;;;AAIH,SAAQY,cAAc,MAAM;AAC3B,UACC,KAAKZ,eAAe,QACpB,OAAOxE,yBAAyB,aAC/B;AACDA,6BAAqB,KAAKwE,UAAU;AACpC,aAAKA,aAAa;;;AAIpB,SAAOpD,4BAA4B,MAAY;AAC9C,WAAKtB,2BAA0B;AAC/B,WAAKjB,qBAAqB,CAAA;;AAc3B,SAAOsC,qBAAqB,CAACX,MAAuB;AACnD,UAAIA,EAAEyD,kBAAkB;AACvB;;AAGD,YAAM,EAAEpF,mBAAkB,IAAK;AAC/B,WAAKA,qBAAqB;AAE1B,YAAM2G,eAAeC,qBAAqBjF,CAAC;AAG3C,UAAI,KAAKsB,QAAQqD,WAAU,GAAI;AAC9B,aAAK3B,QAAQqB,QAAO;AACpB,aAAKO,YAAW;;AAIjB,WAAK5B,QAAQC,UAAU5E,sBAAsB,CAAA,GAAI;QAChD6G,eAAe;QACfjB,uBAAuB,KAAKA;QAC5Be;OACA;AAED,YAAM,EAAEtC,aAAY,IAAK1C;AACzB,YAAMmF,aAAaC,oBAAoB1C,YAAY;AAEnD,UAAI,KAAKpB,QAAQqD,WAAU,GAAI;AAC9B,YAAIjC,gBAAgB,OAAOA,aAAa2C,iBAAiB,YAAY;AAIpE,gBAAM3F,WAAmB,KAAK4B,QAAQC,YAAW;AACjD,gBAAM+D,aAAa,KAAKlH,YAAYoD,IAAI9B,QAAQ;AAChD,gBAAM6F,cAAc,KAAKvH,mBAAmBwD,IAAI9B,QAAQ,KAAK4F;AAE7D,cAAIC,aAAa;AAChB,kBAAM,EAAEzD,SAASC,SAASyD,SAASC,QAAO,IACzC,KAAK5D,mCAAkC;AACxC,kBAAM6D,cAAc;cAAE5D;cAASC;;AAC/B,kBAAM4D,cAAc;cAAEH;cAASC;;AAC/B,kBAAMG,oBAAoBC,qBACzBP,YACAC,aACAP,cACAU,aACAC,WAAW;AAGZjD,yBAAa2C,aACZE,aACAK,kBAAkBE,GAClBF,kBAAkBG,CAAC;;;AAKtB,YAAI;AAEHrD,2BAAY,QAAZA,iBAAY,SAAZA,SAAAA,aAAcsD,QAAQ,oBAAoB,CAAA,CAAE;iBACpCC,KAAK;;AAMd,aAAK/C,yBAAyBlD,EAAEU,MAAM;AAGtC,cAAM,EAAEsB,qBAAoB,IAAK,KAAKH,mCAAkC;AACxE,YAAI,CAACG,sBAAsB;AAM1BsB;YAAW,MAAM,KAAKN,QAAQkD,kBAAiB;YAAI;UAAC;eAC9C;AAWN,eAAKlD,QAAQkD,kBAAiB;;iBAErBf,YAAY;AAEtB,aAAK3C,oBAAoB2C,UAAU;iBAEnCzC,gBACA,CAACA,aAAayD,UACZnG,EAAEU,UAAU,CAAEV,EAAEU,OAAmB0F,gBACpC,CAAEpG,EAAEU,OAAmB0F,aAAa,WAAW,IAC/C;AAID;aACM;AAENpG,UAAEqG,eAAc;;;AAIlB,SAAOxF,0BAA0B,MAAY;AAC5C,UAAI,KAAKvB,2BAA0B,KAAM,KAAKgC,QAAQqD,WAAU,GAAI;AAInE,aAAK3B,QAAQqB,QAAO;;AAErB,WAAKO,YAAW;;AAGjB,SAAO7D,4BAA4B,CAACf,MAAuB;AAC1D,WAAKxB,qBAAqB,CAAA;AAE1B,UAAI,KAAKoD,qBAAoB,GAAI;YAChC;AAAA,SAAA,MAAA,KAAKe,yBAAmB,QAAxB,QAAwB,SAAxB,SAAA,IAA0B2D,iBAAiBtG,EAAE0C,YAAY;;AAG1D,YAAM6D,eAAe,KAAKC,kBAAkBC,MAAMzG,EAAEU,MAAM;AAC1D,UAAI,CAAC6F,gBAAgB,KAAKjF,QAAQqD,WAAU,GAAI;AAC/C;;AAGD,YAAM,EAAEjC,aAAY,IAAK1C;AACzB,YAAMmF,aAAaC,oBAAoB1C,YAAY;AAEnD,UAAIyC,YAAY;AAEf,aAAK3C,oBAAoB2C,YAAYzC,YAAY;;;AAQnD,SAAO5B,qBAAqB,CAACd,MAAuB;AACnD,YAAM,EAAExB,mBAAkB,IAAK;AAC/B,WAAKA,qBAAqB,CAAA;AAE1B,UAAI,CAAC,KAAK8C,QAAQqD,WAAU,GAAI;AAE/B;;AAGD,WAAKjD,gBAAgB1B,EAAE0G;AAKvB,UAAIlI,mBAAmBF,SAAS,GAAG;AAClC,aAAK0E,QAAQ+B,MAAMvG,oBAAoB;UACtCwG,cAAcC,qBAAqBjF,CAAC;SACpC;;AAGF,YAAM2G,UAAUnI,mBAAmB8D;QAAK,CAAChC,aACxC,KAAKgB,QAAQsF,gBAAgBtG,QAAQ;MAAC;AAGvC,UAAIqG,SAAS;AAEZ3G,UAAEqG,eAAc;AAChB,YAAIrG,EAAE0C,cAAc;AACnB1C,YAAE0C,aAAajB,aAAa,KAAKE,qBAAoB;;;;AAKxD,SAAOT,2BAA2B,CAAClB,MAAuB;AACzD,WAAKvB,oBAAoB,CAAA;AAEzB,UAAI,KAAKmD,qBAAoB,GAAI;YAChC;AAAA,SAAA,MAAA,KAAKe,yBAAmB,QAAxB,QAAwB,SAAxB,SAAA,IAA0B2D,iBAAiBtG,EAAE0C,YAAY;;;AAW3D,SAAOzB,oBAAoB,CAACjB,MAAuB;AAClD,YAAM,EAAEvB,kBAAiB,IAAK;AAC9B,WAAKA,oBAAoB,CAAA;AAEzB,UAAI,CAAC,KAAK6C,QAAQqD,WAAU,GAAI;AAG/B3E,UAAEqG,eAAc;AAChB,YAAIrG,EAAE0C,cAAc;AACnB1C,YAAE0C,aAAajB,aAAa;;AAE7B;;AAGD,WAAKC,gBAAgB1B,EAAE0G;AACvB,WAAK3C,mBAAmBkB,qBAAqBjF,CAAC;AAE9C,WAAK6E,cAAcpG,iBAAiB;AAEpC,YAAMkI,WAAWlI,qBAAqB,CAAA,GAAI6D;QAAK,CAAChC,aAC/C,KAAKgB,QAAQsF,gBAAgBtG,QAAQ;MAAC;AAGvC,UAAIqG,SAAS;AAEZ3G,UAAEqG,eAAc;AAChB,YAAIrG,EAAE0C,cAAc;AACnB1C,YAAE0C,aAAajB,aAAa,KAAKE,qBAAoB;;iBAE5C,KAAKC,qBAAoB,GAAI;AAGvC5B,UAAEqG,eAAc;aACV;AACNrG,UAAEqG,eAAc;AAChB,YAAIrG,EAAE0C,cAAc;AACnB1C,YAAE0C,aAAajB,aAAa;;;;AAK/B,SAAOT,4BAA4B,CAAChB,MAAuB;AAC1D,UAAI,KAAK4B,qBAAoB,GAAI;AAChC5B,UAAEqG,eAAc;;AAGjB,YAAMQ,cAAc,KAAKL,kBAAkBM,MAAM9G,EAAEU,MAAM;AACzD,UAAI,CAACmG,aAAa;AACjB;;AAGD,UAAI,KAAKjF,qBAAoB,GAAI;AAChC0B;UAAW,MAAM,KAAKc,kBAAiB;UAAI;QAAC;;AAE7C,WAAKQ,YAAW;;AAGjB,SAAOxD,uBAAuB,CAACpB,MAAuB;AACrD,WAAKzB,gBAAgB,CAAA;AAErB,UAAI,KAAKqD,qBAAoB,GAAI;YAEhC;AADA5B,UAAEqG,eAAc;AAChB,SAAA,MAAA,KAAK1D,yBAAmB,QAAxB,QAAwB,SAAxB,SAAA,IAA0B2D,iBAAiBtG,EAAE0C,YAAY;iBAC/C0C,oBAAoBpF,EAAE0C,YAAY,GAAG;AAK/C1C,UAAEqG,eAAc;;AAGjB,WAAKG,kBAAkBO,MAAK;;AAO7B,SAAO5F,gBAAgB,CAACnB,MAAuB;AAC9C,YAAM,EAAEzB,cAAa,IAAK;AAC1B,WAAKA,gBAAgB,CAAA;AAErB,WAAKyE,QAAQ+B,MAAMxG,eAAe;QACjCyG,cAAcC,qBAAqBjF,CAAC;OACpC;AACD,WAAKgD,QAAQgE,KAAK;QAAEvF,YAAY,KAAKE,qBAAoB;OAAI;AAE7D,UAAI,KAAKC,qBAAoB,GAAI;AAChC,aAAKwC,kBAAiB;iBACZ,KAAK9C,QAAQqD,WAAU,GAAI;AACrC,aAAK3B,QAAQqB,QAAO;;AAErB,WAAKO,YAAW;;AAGjB,SAAO3E,oBAAoB,CAACD,MAAuB;AAClD,YAAMU,SAASV,EAAEU;AAIjB,UAAI,OAAOA,OAAOuG,aAAa,YAAY;AAC1C;;AAID,UACCvG,OAAOwG,YAAY,WACnBxG,OAAOwG,YAAY,YACnBxG,OAAOwG,YAAY,cACnBxG,OAAOyG,mBACN;AACD;;AAKDnH,QAAEqG,eAAc;AAChB3F,aAAOuG,SAAQ;;AAprBf,SAAKtI,UAAU,IAAIyI,cAAcvD,eAAelF,OAAO;AACvD,SAAKqE,UAAUY,QAAQyD,WAAU;AACjC,SAAK/F,UAAUsC,QAAQ0D,WAAU;AACjC,SAAKxE,WAAWc,QAAQ2D,YAAW;AACnC,SAAKf,oBAAoB,IAAIgB,kBAAkB,KAAKjD,gBAAgB;;;;;ACnEtE,IAAIkD;AAEG,SAASC,gBAAkC;AACjD,MAAI,CAACD,YAAY;AAChBA,iBAAa,IAAIE,MAAK;AACtBF,eAAWG,MACV;;AAGF,SAAOH;;;;ACDD,IAAMI,eAA+B,SAASC,cACpDC,SACAC,SACAC,SACmB;AACnB,SAAO,IAAIC,iBAAiBH,SAASC,SAASC,OAAO;;", "names": ["memoize", "fn", "result", "memoized", "without", "items", "item", "filter", "i", "union", "itemsA", "itemsB", "set", "Set", "insertItem", "add", "for<PERSON>ach", "key", "push", "EnterLeave<PERSON><PERSON>nter", "enter", "enteringNode", "<PERSON><PERSON><PERSON><PERSON>", "entered", "length", "isNodeEntered", "node", "isNodeInDocument", "contains", "union", "filter", "leave", "leavingNode", "without", "reset", "NativeDragSource", "initializeExposedProperties", "Object", "keys", "config", "exposeProperties", "for<PERSON>ach", "property", "defineProperty", "item", "configurable", "enumerable", "get", "console", "warn", "loadDataTransfer", "dataTransfer", "newProperties", "propertyFn", "value", "matchesTypes", "defineProperties", "canDrag", "beginDrag", "isDragging", "monitor", "handle", "getSourceId", "endDrag", "FILE", "URL", "TEXT", "HTML", "getDataFromDataTransfer", "dataTransfer", "typesToTry", "defaultValue", "result", "reduce", "resultSoFar", "typeToTry", "getData", "nativeTypesConfig", "FILE", "exposeProperties", "files", "dataTransfer", "Array", "prototype", "slice", "call", "items", "matchesTypes", "HTML", "html", "getDataFromDataTransfer", "URL", "urls", "split", "TEXT", "text", "createNativeDragSource", "type", "dataTransfer", "config", "nativeTypesConfig", "Error", "result", "NativeDragSource", "loadDataTransfer", "matchNativeItemType", "dataTransferTypes", "Array", "prototype", "slice", "call", "types", "Object", "keys", "filter", "nativeItemType", "typeConfig", "matchesTypes", "some", "t", "indexOf", "isFirefox", "memoize", "test", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "window", "safari", "MonotonicInterpolant", "interpolate", "x", "xs", "ys", "c1s", "c2s", "c3s", "i", "length", "low", "high", "mid", "Math", "floor", "xHere", "max", "diff", "diffSq", "indexes", "push", "sort", "a", "b", "dys", "dxs", "ms", "dx", "dy", "m2", "mNext", "dxNext", "common", "m", "c1", "invDx", "ELEMENT_NODE", "getNodeClientOffset", "node", "el", "nodeType", "parentElement", "top", "left", "getBoundingClientRect", "x", "y", "getEventClientOffset", "e", "clientX", "clientY", "isImageNode", "document", "nodeName", "isFirefox", "documentElement", "contains", "getDragPreviewSize", "isImage", "dragPreview", "sourceWidth", "sourceHeight", "dragPreviewWidth", "width", "dragPreviewHeight", "height", "<PERSON><PERSON><PERSON><PERSON>", "window", "devicePixelRatio", "getDragPreviewOffset", "sourceNode", "clientOffset", "anchorPoint", "offsetPoint", "dragPreviewNode", "dragPreviewNodeOffsetFromClient", "offsetFromDragPreview", "offsetWidth", "offsetHeight", "anchorX", "anchorY", "calculateYOffset", "interpolantY", "MonotonicInterpolant", "interpolate", "calculateXOffset", "interpolantX", "offsetX", "offsetY", "isManualOffsetX", "isManualOffsetY", "OptionsReader", "window", "globalContext", "undefined", "document", "rootElement", "optionsArgs", "options", "ownerDocument", "HTML5BackendImpl", "profile", "sourcePreviewNodes", "size", "sourcePreviewNodeOptions", "sourceNodeOptions", "sourceNodes", "dragStartSourceIds", "length", "dropTargetIds", "dragEnterTargetIds", "dragOverTargetIds", "window", "options", "document", "rootElement", "setup", "root", "undefined", "__isReactDndBackendSetUp", "Error", "addEventListeners", "teardown", "removeEventListeners", "clearCurrentDragSourceNode", "asyncEndDragFrameId", "cancelAnimationFrame", "connectDragPreview", "sourceId", "node", "set", "delete", "connectDragSource", "handleDragStart", "e", "handleSelectStart", "setAttribute", "addEventListener", "removeEventListener", "connectDropTarget", "targetId", "handleDragEnter", "handleDragOver", "handleDrop", "target", "handleTopDragStart", "handleTopDragStartCapture", "handleTopDragEndCapture", "handleTopDragEnter", "handleTopDragEnterCapture", "handleTopDragLeaveCapture", "handleTopDragOver", "handleTopDragOverCapture", "handleTopDrop", "handleTopDropCapture", "getCurrentSourceNodeOptions", "monitor", "getSourceId", "get", "dropEffect", "altKeyPressed", "getCurrentDropEffect", "isDraggingNativeItem", "getCurrentSourcePreviewNodeOptions", "anchorX", "anchorY", "captureDraggingState", "itemType", "getItemType", "Object", "keys", "NativeTypes", "some", "key", "beginDragNativeItem", "type", "dataTransfer", "currentNativeSource", "createNativeDragSource", "currentNative<PERSON><PERSON>le", "registry", "addSource", "actions", "beginDrag", "setCurrentDragSourceNode", "currentDragSourceNode", "MOUSE_MOVE_TIMEOUT", "mouseMoveTimeoutTimer", "setTimeout", "endDragIfSourceWasRemovedFromDOM", "clearTimeout", "defaultPrevented", "unshift", "_e", "manager", "globalContext", "Map", "lastClientOffset", "hoverRafId", "getSourceClientOffset", "source", "getNodeClientOffset", "endDragNativeItem", "endDrag", "removeSource", "isNodeInDocument", "Boolean", "body", "contains", "isDragging", "cancelHover", "scheduleHover", "requestAnimationFrame", "hover", "clientOffset", "getEventClientOffset", "publishSource", "nativeType", "matchNativeItemType", "setDragImage", "sourceNode", "dragPreview", "offsetX", "offsetY", "anchorPoint", "offsetPoint", "dragPreviewOffset", "getDragPreviewOffset", "x", "y", "setData", "err", "publishDragSource", "types", "hasAttribute", "preventDefault", "loadDataTransfer", "isFirstEnter", "enterLeaveCounter", "enter", "altKey", "canDrop", "canDropOnTarget", "isLastLeave", "leave", "reset", "drop", "dragDrop", "tagName", "isContentEditable", "OptionsReader", "getActions", "getMonitor", "getRegistry", "EnterLeave<PERSON><PERSON>nter", "emptyImage", "getEmptyImage", "Image", "src", "HTML5Backend", "createBackend", "manager", "context", "options", "HTML5BackendImpl"]}