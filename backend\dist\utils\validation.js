"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isValidEmailDomain = exports.sanitizeSearchQuery = exports.isValidUUID = exports.searchSchema = exports.paginationSchema = exports.passwordSchema = exports.emailSchema = exports.uuidSchema = void 0;
const zod_1 = require("zod");
exports.uuidSchema = zod_1.z.string().uuid('Invalid UUID format');
exports.emailSchema = zod_1.z.string().email('Invalid email format');
exports.passwordSchema = zod_1.z.string().min(8, 'Password must be at least 8 characters');
exports.paginationSchema = zod_1.z.object({
    page: zod_1.z.coerce.number().int().min(1).default(1),
    limit: zod_1.z.coerce.number().int().min(1).max(100).default(20),
    sortBy: zod_1.z.string().optional(),
    sortOrder: zod_1.z.enum(['asc', 'desc']).default('desc'),
});
exports.searchSchema = zod_1.z.object({
    q: zod_1.z.string().min(1, 'Search query is required').max(100),
    ...exports.paginationSchema.shape,
});
const isValidUUID = (value) => {
    try {
        exports.uuidSchema.parse(value);
        return true;
    }
    catch {
        return false;
    }
};
exports.isValidUUID = isValidUUID;
const sanitizeSearchQuery = (query) => {
    return query.trim().replace(/[^\w\s-]/g, '').substring(0, 100);
};
exports.sanitizeSearchQuery = sanitizeSearchQuery;
const isValidEmailDomain = (email, allowedDomains) => {
    if (!allowedDomains || allowedDomains.length === 0) {
        return true;
    }
    const domain = email.split('@')[1]?.toLowerCase();
    return allowedDomains.some(allowedDomain => domain === allowedDomain.toLowerCase() ||
        domain?.endsWith(`.${allowedDomain.toLowerCase()}`));
};
exports.isValidEmailDomain = isValidEmailDomain;
//# sourceMappingURL=validation.js.map