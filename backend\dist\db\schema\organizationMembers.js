"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PERMISSIONS = exports.ROLE_HIERARCHY = exports.updateOrganizationMemberSchema = exports.selectOrganizationMemberSchema = exports.insertOrganizationMemberSchema = exports.organizationMembers = exports.memberStatusEnum = exports.roleEnum = void 0;
const pg_core_1 = require("drizzle-orm/pg-core");
const drizzle_zod_1 = require("drizzle-zod");
const zod_1 = require("zod");
const organizations_1 = require("./organizations");
const users_1 = require("./users");
exports.roleEnum = (0, pg_core_1.pgEnum)('role', ['viewer', 'member', 'admin', 'owner']);
exports.memberStatusEnum = (0, pg_core_1.pgEnum)('member_status', ['active', 'inactive', 'pending']);
exports.organizationMembers = (0, pg_core_1.pgTable)('organization_members', {
    id: (0, pg_core_1.uuid)('id').primaryKey().defaultRandom(),
    organizationId: (0, pg_core_1.uuid)('organization_id').notNull(),
    userId: (0, pg_core_1.uuid)('user_id').notNull(),
    role: (0, exports.roleEnum)('role').notNull().default('member'),
    status: (0, exports.memberStatusEnum)('status').notNull().default('active'),
    invitedBy: (0, pg_core_1.uuid)('invited_by'),
    joinedAt: (0, pg_core_1.timestamp)('joined_at').defaultNow().notNull(),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow().notNull(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').defaultNow().notNull(),
}, (table) => ({
    uniqueOrgUser: (0, pg_core_1.unique)().on(table.organizationId, table.userId),
    organizationFk: (0, pg_core_1.foreignKey)({
        columns: [table.organizationId],
        foreignColumns: [organizations_1.organizations.id],
        name: 'organization_members_organization_id_fk'
    }).onDelete('cascade'),
    userFk: (0, pg_core_1.foreignKey)({
        columns: [table.userId],
        foreignColumns: [users_1.users.id],
        name: 'organization_members_user_id_fk'
    }).onDelete('cascade'),
    invitedByFk: (0, pg_core_1.foreignKey)({
        columns: [table.invitedBy],
        foreignColumns: [users_1.users.id],
        name: 'organization_members_invited_by_fk'
    }).onDelete('set null'),
}));
exports.insertOrganizationMemberSchema = (0, drizzle_zod_1.createInsertSchema)(exports.organizationMembers, {
    organizationId: zod_1.z.string().uuid('Invalid organization ID'),
    userId: zod_1.z.string().uuid('Invalid user ID'),
    role: zod_1.z.enum(['viewer', 'member', 'admin', 'owner']),
    status: zod_1.z.enum(['active', 'inactive', 'pending']).optional(),
    invitedBy: zod_1.z.string().uuid('Invalid inviter ID').optional(),
});
exports.selectOrganizationMemberSchema = (0, drizzle_zod_1.createSelectSchema)(exports.organizationMembers);
exports.updateOrganizationMemberSchema = zod_1.z.object({
    role: zod_1.z.enum(['viewer', 'member', 'admin', 'owner']).optional(),
    status: zod_1.z.enum(['active', 'inactive', 'pending']).optional(),
});
exports.ROLE_HIERARCHY = {
    viewer: 0,
    member: 1,
    admin: 2,
    owner: 3,
};
exports.PERMISSIONS = {
    'org:read': ['viewer', 'member', 'admin', 'owner'],
    'org:update': ['admin', 'owner'],
    'org:delete': ['owner'],
    'org:manage_members': ['admin', 'owner'],
    'org:invite_members': ['admin', 'owner'],
    'board:read': ['viewer', 'member', 'admin', 'owner'],
    'board:create': ['member', 'admin', 'owner'],
    'board:update': ['admin', 'owner'],
    'board:delete': ['admin', 'owner'],
    'board:manage_columns': ['admin', 'owner'],
    'card:read': ['viewer', 'member', 'admin', 'owner'],
    'card:create': ['member', 'admin', 'owner'],
    'card:update': ['member', 'admin', 'owner'],
    'card:delete': ['member', 'admin', 'owner'],
    'card:assign': ['member', 'admin', 'owner'],
    'comment:read': ['viewer', 'member', 'admin', 'owner'],
    'comment:create': ['member', 'admin', 'owner'],
    'comment:update': ['member', 'admin', 'owner'],
    'comment:delete': ['member', 'admin', 'owner'],
};
//# sourceMappingURL=organizationMembers.js.map