import type { AcceptInvitationData, Role } from '../db/schema';
export declare class InvitationService {
    sendInvitation(organizationId: string, email: string, role: Role, invitedBy: string): Promise<{
        invitation: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            status: "pending" | "accepted" | "declined" | "expired";
            email: string;
            role: "viewer" | "member" | "admin" | "owner";
            organizationId: string;
            invitedBy: string;
            token: string;
            tempPassword: string | null;
            acceptedBy: string | null;
            expiresAt: Date;
            acceptedAt: Date | null;
        } | undefined;
        message: string;
    }>;
    getInvitationByToken(token: string): Promise<{
        invitation: {
            id: string;
            organizationId: string;
            email: string;
            role: "viewer" | "member" | "admin" | "owner";
            token: string;
            tempPassword: string | null;
            status: "pending" | "accepted" | "declined" | "expired";
            invitedBy: string;
            acceptedBy: string | null;
            expiresAt: Date;
            acceptedAt: Date | null;
            createdAt: Date;
            updatedAt: Date;
        };
        organization: {
            id: string;
            name: string;
            logo: string | null;
            domain: string | null;
        };
    } | undefined>;
    acceptInvitation(token: string, acceptData: AcceptInvitationData): Promise<{
        user: import("../db/schema").UserWithoutPassword | null;
        organization: {
            id: string;
            name: string;
            logo: string | null;
            domain: string | null;
        };
        membership: {
            role: "viewer" | "member" | "admin" | "owner";
            status: "active";
        };
        isNewUser: boolean;
        message: string;
    }>;
    declineInvitation(token: string): Promise<{
        message: string;
    }>;
    resendInvitation(invitationId: string, requestingUserId: string): Promise<{
        message: string;
    }>;
    cleanupExpiredInvitations(): Promise<{
        expiredCount: number;
        message: string;
    }>;
    getOrganizationInvitations(organizationId: string): Promise<{
        invitation: {
            id: string;
            organizationId: string;
            email: string;
            role: "viewer" | "member" | "admin" | "owner";
            token: string;
            tempPassword: string | null;
            status: "pending" | "accepted" | "declined" | "expired";
            invitedBy: string;
            acceptedBy: string | null;
            expiresAt: Date;
            acceptedAt: Date | null;
            createdAt: Date;
            updatedAt: Date;
        };
        inviter: {
            firstName: string;
            lastName: string;
            email: string;
        };
    }[]>;
}
export declare const invitationService: InvitationService;
//# sourceMappingURL=invitationService.d.ts.map