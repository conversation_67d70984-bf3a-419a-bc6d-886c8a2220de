import type { NewOrganization, UpdateOrganization, Organization } from '../db/schema';
export declare class OrganizationService {
    createOrganization(organizationData: Omit<NewOrganization, 'id' | 'createdAt' | 'updatedAt'>, creatorId: string): Promise<Organization>;
    getOrganizationById(organizationId: string): Promise<Organization | null>;
    getOrganizationBySlug(slug: string): Promise<Organization | null>;
    updateOrganization(organizationId: string, updates: UpdateOrganization, userId: string): Promise<Organization>;
    deleteOrganization(organizationId: string, userId: string): Promise<void>;
    getOrganizationMembers(organizationId: string): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            avatar: string | null;
            status: "active" | "inactive" | "pending" | "suspended";
            lastLoginAt: Date | null;
        };
        membership: {
            id: string;
            role: "viewer" | "member" | "admin" | "owner";
            status: "active" | "inactive" | "pending";
            joinedAt: Date;
        };
    }[]>;
    searchUserOrganizations(userId: string, query?: string): Promise<{
        organization: {
            id: string;
            name: string;
            slug: string;
            domain: string | null;
            description: string | null;
            logo: string | null;
            isActive: boolean;
            createdAt: Date;
        };
        membership: {
            role: "viewer" | "member" | "admin" | "owner";
            status: "active" | "inactive" | "pending";
            joinedAt: Date;
        };
    }[]>;
    getOrganizationStats(organizationId: string): Promise<{
        members: Record<string, number>;
        totalMembers: number;
    }>;
    private slugExists;
    domainExists(domain: string): Promise<boolean>;
    getOrganizationByDomain(domain: string): Promise<Organization | null>;
    switchOrganization(userId: string, organizationId: string): Promise<{
        organization: {
            id: string;
            name: string;
            slug: string;
            domain: string | null;
            description: string | null;
            logo: string | null;
            settings: {
                allowDomainSignup?: boolean;
                requireInvitation?: boolean;
                defaultRole?: "viewer" | "member" | "admin";
                features?: string[];
                branding?: {
                    primaryColor?: string;
                    secondaryColor?: string;
                    logoUrl?: string;
                };
            } | null;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
        };
        membership: {
            id: string;
            role: "viewer" | "member" | "admin" | "owner";
            status: "active" | "inactive" | "pending";
        } | undefined;
    }>;
}
export declare const organizationService: OrganizationService;
//# sourceMappingURL=organizationService.d.ts.map