"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.organizationService = exports.OrganizationService = void 0;
const connection_1 = require("../db/connection");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const errorHandler_1 = require("../middleware/errorHandler");
const crypto_1 = require("../utils/crypto");
class OrganizationService {
    async createOrganization(organizationData, creatorId) {
        try {
            const slug = await (0, crypto_1.generateUniqueSlug)(organizationData.name, async (slug) => await this.slugExists(slug));
            const newOrganization = await connection_1.db
                .insert(schema_1.organizations)
                .values({
                ...organizationData,
                slug,
                settings: organizationData.settings || {
                    allowDomainSignup: false,
                    requireInvitation: true,
                    defaultRole: 'member',
                    features: [],
                },
            })
                .returning();
            const organization = newOrganization[0];
            await connection_1.db.insert(schema_1.organizationMembers).values({
                organizationId: organization.id,
                userId: creatorId,
                role: 'owner',
                status: 'active',
                invitedBy: creatorId,
            });
            return organization;
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('duplicate key')) {
                throw (0, errorHandler_1.createError)('Organization with this name or domain already exists', 409);
            }
            throw error;
        }
    }
    async getOrganizationById(organizationId) {
        const organization = await connection_1.db
            .select()
            .from(schema_1.organizations)
            .where((0, drizzle_orm_1.eq)(schema_1.organizations.id, organizationId))
            .limit(1);
        return organization.length > 0 ? organization[0] : null;
    }
    async getOrganizationBySlug(slug) {
        const organization = await connection_1.db
            .select()
            .from(schema_1.organizations)
            .where((0, drizzle_orm_1.eq)(schema_1.organizations.slug, slug))
            .limit(1);
        return organization.length > 0 ? organization[0] : null;
    }
    async updateOrganization(organizationId, updates, userId) {
        const existingOrg = await this.getOrganizationById(organizationId);
        if (!existingOrg) {
            throw (0, errorHandler_1.createError)('Organization not found', 404);
        }
        if (updates.slug && updates.slug !== existingOrg.slug) {
            const slugExists = await this.slugExists(updates.slug);
            if (slugExists) {
                throw (0, errorHandler_1.createError)('Organization slug already exists', 409);
            }
        }
        const updatedOrganization = await connection_1.db
            .update(schema_1.organizations)
            .set({
            ...updates,
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.organizations.id, organizationId))
            .returning();
        if (!updatedOrganization.length) {
            throw (0, errorHandler_1.createError)('Failed to update organization', 500);
        }
        return updatedOrganization[0];
    }
    async deleteOrganization(organizationId, userId) {
        const existingOrg = await this.getOrganizationById(organizationId);
        if (!existingOrg) {
            throw (0, errorHandler_1.createError)('Organization not found', 404);
        }
        await connection_1.db
            .update(schema_1.organizations)
            .set({
            isActive: false,
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.organizations.id, organizationId));
    }
    async getOrganizationMembers(organizationId) {
        const members = await connection_1.db
            .select({
            user: {
                id: schema_1.users.id,
                email: schema_1.users.email,
                firstName: schema_1.users.firstName,
                lastName: schema_1.users.lastName,
                avatar: schema_1.users.avatar,
                status: schema_1.users.status,
                lastLoginAt: schema_1.users.lastLoginAt,
            },
            membership: {
                id: schema_1.organizationMembers.id,
                role: schema_1.organizationMembers.role,
                status: schema_1.organizationMembers.status,
                joinedAt: schema_1.organizationMembers.joinedAt,
            },
        })
            .from(schema_1.organizationMembers)
            .innerJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.users.id, schema_1.organizationMembers.userId))
            .where((0, drizzle_orm_1.eq)(schema_1.organizationMembers.organizationId, organizationId))
            .orderBy(schema_1.organizationMembers.joinedAt);
        return members;
    }
    async searchUserOrganizations(userId, query) {
        let whereCondition = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.organizationMembers.userId, userId), (0, drizzle_orm_1.eq)(schema_1.organizationMembers.status, 'active'), (0, drizzle_orm_1.eq)(schema_1.organizations.isActive, true));
        if (query) {
            whereCondition = (0, drizzle_orm_1.and)(whereCondition, (0, drizzle_orm_1.or)((0, drizzle_orm_1.like)(schema_1.organizations.name, `%${query}%`), (0, drizzle_orm_1.like)(schema_1.organizations.slug, `%${query}%`)));
        }
        const userOrganizations = await connection_1.db
            .select({
            organization: {
                id: schema_1.organizations.id,
                name: schema_1.organizations.name,
                slug: schema_1.organizations.slug,
                domain: schema_1.organizations.domain,
                description: schema_1.organizations.description,
                logo: schema_1.organizations.logo,
                isActive: schema_1.organizations.isActive,
                createdAt: schema_1.organizations.createdAt,
            },
            membership: {
                role: schema_1.organizationMembers.role,
                status: schema_1.organizationMembers.status,
                joinedAt: schema_1.organizationMembers.joinedAt,
            },
        })
            .from(schema_1.organizationMembers)
            .innerJoin(schema_1.organizations, (0, drizzle_orm_1.eq)(schema_1.organizations.id, schema_1.organizationMembers.organizationId))
            .where(whereCondition)
            .orderBy(schema_1.organizationMembers.joinedAt);
        return userOrganizations;
    }
    async getOrganizationStats(organizationId) {
        const memberStats = await connection_1.db
            .select({
            role: schema_1.organizationMembers.role,
            count: schema_1.organizationMembers.id,
        })
            .from(schema_1.organizationMembers)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.organizationMembers.organizationId, organizationId), (0, drizzle_orm_1.eq)(schema_1.organizationMembers.status, 'active')));
        return {
            members: memberStats.reduce((acc, stat) => {
                acc[stat.role] = (acc[stat.role] || 0) + 1;
                return acc;
            }, {}),
            totalMembers: memberStats.length,
        };
    }
    async slugExists(slug) {
        const existing = await connection_1.db
            .select({ id: schema_1.organizations.id })
            .from(schema_1.organizations)
            .where((0, drizzle_orm_1.eq)(schema_1.organizations.slug, slug))
            .limit(1);
        return existing.length > 0;
    }
    async domainExists(domain) {
        const existing = await connection_1.db
            .select({ id: schema_1.organizations.id })
            .from(schema_1.organizations)
            .where((0, drizzle_orm_1.eq)(schema_1.organizations.domain, domain))
            .limit(1);
        return existing.length > 0;
    }
    async getOrganizationByDomain(domain) {
        const organization = await connection_1.db
            .select()
            .from(schema_1.organizations)
            .where((0, drizzle_orm_1.eq)(schema_1.organizations.domain, domain))
            .limit(1);
        return organization.length > 0 ? organization[0] : null;
    }
    async switchOrganization(userId, organizationId) {
        const membership = await connection_1.db
            .select({
            id: schema_1.organizationMembers.id,
            role: schema_1.organizationMembers.role,
            status: schema_1.organizationMembers.status,
        })
            .from(schema_1.organizationMembers)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.organizationMembers.userId, userId), (0, drizzle_orm_1.eq)(schema_1.organizationMembers.organizationId, organizationId), (0, drizzle_orm_1.eq)(schema_1.organizationMembers.status, 'active')))
            .limit(1);
        if (!membership.length) {
            throw (0, errorHandler_1.createError)('User is not a member of this organization', 403);
        }
        const organization = await this.getOrganizationById(organizationId);
        if (!organization || !organization.isActive) {
            throw (0, errorHandler_1.createError)('Organization not found or inactive', 404);
        }
        return {
            organization,
            membership: membership[0],
        };
    }
}
exports.OrganizationService = OrganizationService;
exports.organizationService = new OrganizationService();
//# sourceMappingURL=organizationService.js.map