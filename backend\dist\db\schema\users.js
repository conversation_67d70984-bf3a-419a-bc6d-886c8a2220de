"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.changePasswordSchema = exports.registerSchema = exports.loginSchema = exports.updateUserSchema = exports.selectUserSchema = exports.insertUserSchema = exports.users = exports.userStatusEnum = void 0;
const pg_core_1 = require("drizzle-orm/pg-core");
const zod_1 = require("zod");
exports.userStatusEnum = (0, pg_core_1.pgEnum)('user_status', ['active', 'inactive', 'pending', 'suspended']);
exports.users = (0, pg_core_1.pgTable)('users', {
    id: (0, pg_core_1.uuid)('id').primaryKey().defaultRandom(),
    email: (0, pg_core_1.varchar)('email', { length: 255 }).notNull().unique(),
    passwordHash: (0, pg_core_1.varchar)('password_hash', { length: 255 }).notNull(),
    firstName: (0, pg_core_1.varchar)('first_name', { length: 100 }).notNull(),
    lastName: (0, pg_core_1.varchar)('last_name', { length: 100 }).notNull(),
    avatar: (0, pg_core_1.varchar)('avatar', { length: 500 }),
    bio: (0, pg_core_1.text)('bio'),
    phone: (0, pg_core_1.varchar)('phone', { length: 20 }),
    timezone: (0, pg_core_1.varchar)('timezone', { length: 50 }).default('UTC'),
    language: (0, pg_core_1.varchar)('language', { length: 10 }).default('en'),
    status: (0, exports.userStatusEnum)('status').default('active').notNull(),
    isEmailVerified: (0, pg_core_1.boolean)('is_email_verified').default(false).notNull(),
    lastLoginAt: (0, pg_core_1.timestamp)('last_login_at'),
    passwordChangedAt: (0, pg_core_1.timestamp)('password_changed_at'),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow().notNull(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').defaultNow().notNull(),
});
exports.insertUserSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email format'),
    firstName: zod_1.z.string().min(1, 'First name is required').max(100),
    lastName: zod_1.z.string().min(1, 'Last name is required').max(100),
    phone: zod_1.z.string().max(20).optional(),
    timezone: zod_1.z.string().max(50).optional(),
    language: zod_1.z.string().max(10).optional(),
    bio: zod_1.z.string().max(500).optional(),
    avatar: zod_1.z.string().url('Invalid avatar URL').optional(),
    status: zod_1.z.enum(['active', 'inactive', 'pending', 'suspended']).default('active'),
});
exports.selectUserSchema = zod_1.z.object({
    id: zod_1.z.string(),
    email: zod_1.z.string().email(),
    firstName: zod_1.z.string(),
    lastName: zod_1.z.string(),
    phone: zod_1.z.string().nullable(),
    timezone: zod_1.z.string().nullable(),
    language: zod_1.z.string().nullable(),
    bio: zod_1.z.string().nullable(),
    avatar: zod_1.z.string().nullable(),
    status: zod_1.z.enum(['active', 'inactive', 'pending', 'suspended']),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date(),
    lastLoginAt: zod_1.z.date().nullable(),
    passwordChangedAt: zod_1.z.date().nullable(),
});
exports.updateUserSchema = exports.insertUserSchema.partial();
exports.loginSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email format'),
    password: zod_1.z.string().min(1, 'Password is required'),
});
exports.registerSchema = exports.insertUserSchema.extend({
    password: zod_1.z.string().min(8, 'Password must be at least 8 characters'),
    confirmPassword: zod_1.z.string(),
}).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
});
exports.changePasswordSchema = zod_1.z.object({
    currentPassword: zod_1.z.string().min(1, 'Current password is required'),
    newPassword: zod_1.z.string().min(8, 'New password must be at least 8 characters'),
    confirmPassword: zod_1.z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
});
//# sourceMappingURL=users.js.map