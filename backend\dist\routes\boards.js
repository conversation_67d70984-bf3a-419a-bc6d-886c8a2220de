"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const boardService_1 = require("../services/boardService");
const schema_1 = require("../db/schema");
const router = (0, express_1.Router)();
router.get('/organization/:organizationId', auth_1.validateOrganization, (0, auth_1.requirePermission)('board:read'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { organizationId } = req.params;
    const includeArchived = req.query.includeArchived === 'true';
    const boards = await boardService_1.boardService.getOrganizationBoards(organizationId, includeArchived);
    res.json({
        success: true,
        data: {
            boards,
        },
    });
}));
router.post('/organization/:organizationId', auth_1.validateOrganization, (0, auth_1.requirePermission)('board:create'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { organizationId } = req.params;
    const validatedData = schema_1.insertBoardSchema.parse({
        ...req.body,
        organizationId,
        createdBy: req.user.id,
    });
    const board = await boardService_1.boardService.createBoard(validatedData, req.user.id);
    res.status(201).json({
        success: true,
        data: {
            board,
            message: 'Board created successfully',
        },
    });
}));
router.get('/:boardId', (0, auth_1.requirePermission)('board:read'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { boardId } = req.params;
    const includeColumns = req.query.includeColumns !== 'false';
    const board = await boardService_1.boardService.getBoardById(boardId, includeColumns);
    const stats = await boardService_1.boardService.getBoardStats(boardId);
    res.json({
        success: true,
        data: {
            board,
            stats,
        },
    });
}));
router.patch('/:boardId', (0, auth_1.requirePermission)('board:update'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { boardId } = req.params;
    const validatedData = schema_1.updateBoardSchema.parse(req.body);
    const updatedBoard = await boardService_1.boardService.updateBoard(boardId, validatedData, req.user.id);
    res.json({
        success: true,
        data: {
            board: updatedBoard,
            message: 'Board updated successfully',
        },
    });
}));
router.delete('/:boardId', (0, auth_1.requirePermission)('board:delete'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { boardId } = req.params;
    await boardService_1.boardService.deleteBoard(boardId, req.user.id);
    res.json({
        success: true,
        data: {
            message: 'Board deleted successfully',
        },
    });
}));
router.get('/:boardId/columns', (0, auth_1.requirePermission)('board:read'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { boardId } = req.params;
    const columns = await boardService_1.boardService.getBoardColumns(boardId);
    res.json({
        success: true,
        data: {
            columns,
        },
    });
}));
router.post('/:boardId/columns', (0, auth_1.requirePermission)('board:manage_columns'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { boardId } = req.params;
    const validatedData = schema_1.insertBoardColumnSchema.parse({
        ...req.body,
        boardId,
    });
    const column = await boardService_1.boardService.createBoardColumn(validatedData);
    res.status(201).json({
        success: true,
        data: {
            column,
            message: 'Column created successfully',
        },
    });
}));
router.patch('/:boardId/columns/:columnId', (0, auth_1.requirePermission)('board:manage_columns'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { columnId } = req.params;
    const validatedData = schema_1.updateBoardColumnSchema.parse(req.body);
    const updatedColumn = await boardService_1.boardService.updateBoardColumn(columnId, validatedData);
    res.json({
        success: true,
        data: {
            column: updatedColumn,
            message: 'Column updated successfully',
        },
    });
}));
router.delete('/:boardId/columns/:columnId', (0, auth_1.requirePermission)('board:manage_columns'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { columnId } = req.params;
    await boardService_1.boardService.deleteBoardColumn(columnId);
    res.json({
        success: true,
        data: {
            message: 'Column deleted successfully',
        },
    });
}));
router.post('/:boardId/columns/reorder', (0, auth_1.requirePermission)('board:manage_columns'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { boardId } = req.params;
    const { columnIds } = schema_1.reorderColumnsSchema.parse(req.body);
    const reorderedColumns = await boardService_1.boardService.reorderColumns(boardId, columnIds);
    res.json({
        success: true,
        data: {
            columns: reorderedColumns,
            message: 'Columns reordered successfully',
        },
    });
}));
exports.default = router;
//# sourceMappingURL=boards.js.map