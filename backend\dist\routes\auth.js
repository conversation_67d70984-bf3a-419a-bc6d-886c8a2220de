"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authService_1 = require("../services/authService");
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const schema_1 = require("../db/schema");
const router = (0, express_1.Router)();
router.post('/register', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = schema_1.registerSchema.parse(req.body);
    const user = await authService_1.authService.register(validatedData);
    req.session.userId = user.id;
    res.status(201).json({
        success: true,
        data: {
            user,
            message: 'Registration successful',
        },
    });
}));
router.post('/login', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = schema_1.loginSchema.parse(req.body);
    const user = await authService_1.authService.login(validatedData);
    req.session.userId = user.id;
    res.json({
        success: true,
        data: {
            user,
            message: 'Login successful',
        },
    });
}));
router.post('/logout', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (req.session) {
        req.session.destroy((err) => {
            if (err) {
                throw (0, errorHandler_1.createError)('Failed to logout', 500);
            }
            res.clearCookie(process.env.SESSION_NAME || 'agno_session');
            res.json({
                success: true,
                data: {
                    message: 'Logout successful',
                },
            });
        });
    }
    else {
        res.json({
            success: true,
            data: {
                message: 'Already logged out',
            },
        });
    }
}));
router.get('/me', auth_1.validateSession, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('User not found in session', 401);
    }
    const user = await authService_1.authService.getUserById(req.user.id);
    if (!user) {
        throw (0, errorHandler_1.createError)('User not found', 404);
    }
    const organizations = await authService_1.authService.getUserOrganizations(req.user.id);
    res.json({
        success: true,
        data: {
            user,
            organizations,
        },
    });
}));
router.patch('/profile', auth_1.validateSession, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('User not found in session', 401);
    }
    const allowedFields = ['firstName', 'lastName', 'avatar', 'bio', 'phone', 'timezone', 'language'];
    const updates = {};
    for (const field of allowedFields) {
        if (req.body[field] !== undefined) {
            updates[field] = req.body[field];
        }
    }
    if (Object.keys(updates).length === 0) {
        throw (0, errorHandler_1.createError)('No valid fields provided for update', 400);
    }
    const updatedUser = await authService_1.authService.updateUser(req.user.id, updates);
    res.json({
        success: true,
        data: {
            user: updatedUser,
            message: 'Profile updated successfully',
        },
    });
}));
router.post('/change-password', auth_1.validateSession, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('User not found in session', 401);
    }
    const validatedData = schema_1.changePasswordSchema.parse(req.body);
    await authService_1.authService.changePassword(req.user.id, validatedData.currentPassword, validatedData.newPassword);
    res.json({
        success: true,
        data: {
            message: 'Password changed successfully',
        },
    });
}));
router.get('/status', auth_1.optionalAuth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({
        success: true,
        data: {
            isAuthenticated: !!req.user,
            user: req.user || null,
        },
    });
}));
router.post('/refresh', auth_1.validateSession, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({
        success: true,
        data: {
            message: 'Session refreshed successfully',
            user: req.user,
        },
    });
}));
exports.default = router;
//# sourceMappingURL=auth.js.map