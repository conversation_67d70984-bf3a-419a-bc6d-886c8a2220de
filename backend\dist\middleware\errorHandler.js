"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.createError = exports.errorHandler = exports.CustomError = void 0;
const zod_1 = require("zod");
class CustomError extends Error {
    statusCode;
    isOperational;
    constructor(message, statusCode = 500, isOperational = true) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.CustomError = CustomError;
const errorHandler = (error, req, res, next) => {
    let statusCode = 500;
    let message = 'Internal Server Error';
    let details = undefined;
    if (error instanceof zod_1.ZodError) {
        statusCode = 400;
        message = 'Validation Error';
        details = error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
        }));
    }
    else if (error instanceof CustomError) {
        statusCode = error.statusCode;
        message = error.message;
    }
    else if ('statusCode' in error && error.statusCode) {
        statusCode = error.statusCode;
        message = error.message;
    }
    else if (error.message.includes('duplicate key')) {
        statusCode = 409;
        message = 'Resource already exists';
    }
    else if (error.message.includes('foreign key')) {
        statusCode = 400;
        message = 'Invalid reference to related resource';
    }
    else if (error.message.includes('unauthorized') || error.message.includes('authentication')) {
        statusCode = 401;
        message = 'Authentication required';
    }
    else if (error.message.includes('forbidden') || error.message.includes('permission')) {
        statusCode = 403;
        message = 'Insufficient permissions';
    }
    if (statusCode >= 500) {
        console.error('Server Error:', {
            message: error.message,
            stack: error.stack,
            url: req.url,
            method: req.method,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
        });
    }
    res.status(statusCode).json({
        success: false,
        error: {
            message,
            ...(details && { details }),
            ...(process.env.NODE_ENV === 'development' && {
                stack: error.stack,
                originalMessage: error.message,
            }),
        },
        timestamp: new Date().toISOString(),
        path: req.path,
    });
};
exports.errorHandler = errorHandler;
const createError = (message, statusCode = 500) => {
    return new CustomError(message, statusCode);
};
exports.createError = createError;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
//# sourceMappingURL=errorHandler.js.map