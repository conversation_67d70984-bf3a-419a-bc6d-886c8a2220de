"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cardService = exports.CardService = void 0;
const connection_1 = require("../db/connection");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const errorHandler_1 = require("../middleware/errorHandler");
class CardService {
    async createCard(cardData, createdBy) {
        try {
            const boardColumn = await connection_1.db
                .select({
                boardId: schema_1.boardColumns.boardId,
                columnId: schema_1.boardColumns.id,
            })
                .from(schema_1.boardColumns)
                .innerJoin(schema_1.boards, (0, drizzle_orm_1.eq)(schema_1.boards.id, schema_1.boardColumns.boardId))
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.boardColumns.id, cardData.columnId), (0, drizzle_orm_1.eq)(schema_1.boards.id, cardData.boardId), (0, drizzle_orm_1.eq)(schema_1.boards.isArchived, false)))
                .limit(1);
            if (!boardColumn.length) {
                throw (0, errorHandler_1.createError)('Board or column not found', 404);
            }
            if (cardData.position === undefined) {
                const lastCard = await connection_1.db
                    .select({ position: schema_1.cards.position })
                    .from(schema_1.cards)
                    .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.cards.columnId, cardData.columnId), (0, drizzle_orm_1.eq)(schema_1.cards.isArchived, false)))
                    .orderBy((0, drizzle_orm_1.desc)(schema_1.cards.position))
                    .limit(1);
                cardData.position = lastCard.length > 0 ? lastCard[0].position + 1 : 0;
            }
            const newCard = await connection_1.db
                .insert(schema_1.cards)
                .values({
                ...cardData,
                createdBy,
            })
                .returning();
            return newCard[0];
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('foreign key')) {
                throw (0, errorHandler_1.createError)('Invalid board or column reference', 400);
            }
            throw error;
        }
    }
    async getCardById(cardId, includeDetails = true) {
        const card = await connection_1.db
            .select()
            .from(schema_1.cards)
            .where((0, drizzle_orm_1.eq)(schema_1.cards.id, cardId))
            .limit(1);
        if (!card.length) {
            throw (0, errorHandler_1.createError)('Card not found', 404);
        }
        const cardData = card[0];
        if (includeDetails) {
            const assignments = await this.getCardAssignments(cardId);
            const comments = await this.getCardComments(cardId);
            const checklists = await this.getCardChecklists(cardId);
            return {
                ...cardData,
                assignments,
                comments,
                checklists,
            };
        }
        return cardData;
    }
    async getBoardCards(boardId, includeArchived = false) {
        let whereCondition = (0, drizzle_orm_1.eq)(schema_1.cards.boardId, boardId);
        if (!includeArchived) {
            whereCondition = (0, drizzle_orm_1.and)(whereCondition, (0, drizzle_orm_1.eq)(schema_1.cards.isArchived, false));
        }
        const boardCards = await connection_1.db
            .select({
            card: schema_1.cards,
            column: {
                id: schema_1.boardColumns.id,
                name: schema_1.boardColumns.name,
                position: schema_1.boardColumns.position,
            },
        })
            .from(schema_1.cards)
            .innerJoin(schema_1.boardColumns, (0, drizzle_orm_1.eq)(schema_1.boardColumns.id, schema_1.cards.columnId))
            .where(whereCondition)
            .orderBy((0, drizzle_orm_1.asc)(schema_1.boardColumns.position), (0, drizzle_orm_1.asc)(schema_1.cards.position));
        const cardsByColumn = boardCards.reduce((acc, item) => {
            const columnId = item.column.id;
            if (!acc[columnId]) {
                acc[columnId] = {
                    column: item.column,
                    cards: [],
                };
            }
            acc[columnId].cards.push(item.card);
            return acc;
        }, {});
        return Object.values(cardsByColumn);
    }
    async updateCard(cardId, updates, userId) {
        const updatedCard = await connection_1.db
            .update(schema_1.cards)
            .set({
            ...updates,
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.cards.id, cardId))
            .returning();
        if (!updatedCard.length) {
            throw (0, errorHandler_1.createError)('Card not found', 404);
        }
        return updatedCard[0];
    }
    async moveCard(cardId, columnId, position, userId) {
        const existingCard = await connection_1.db
            .select()
            .from(schema_1.cards)
            .where((0, drizzle_orm_1.eq)(schema_1.cards.id, cardId))
            .limit(1);
        if (!existingCard.length) {
            throw (0, errorHandler_1.createError)('Card not found', 404);
        }
        const column = await connection_1.db
            .select({ boardId: schema_1.boardColumns.boardId })
            .from(schema_1.boardColumns)
            .where((0, drizzle_orm_1.eq)(schema_1.boardColumns.id, columnId))
            .limit(1);
        if (!column.length || column[0].boardId !== existingCard[0].boardId) {
            throw (0, errorHandler_1.createError)('Invalid column for this board', 400);
        }
        const updatedCard = await connection_1.db
            .update(schema_1.cards)
            .set({
            columnId,
            position,
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.cards.id, cardId))
            .returning();
        return updatedCard[0];
    }
    async deleteCard(cardId, userId) {
        const result = await connection_1.db
            .update(schema_1.cards)
            .set({
            isArchived: true,
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.cards.id, cardId))
            .returning({ id: schema_1.cards.id });
        if (!result.length) {
            throw (0, errorHandler_1.createError)('Card not found', 404);
        }
    }
    async assignUserToCard(cardId, userId, assignedBy) {
        try {
            const existingAssignment = await connection_1.db
                .select({ id: schema_1.cardAssignments.id })
                .from(schema_1.cardAssignments)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.cardAssignments.cardId, cardId), (0, drizzle_orm_1.eq)(schema_1.cardAssignments.userId, userId)))
                .limit(1);
            if (existingAssignment.length > 0) {
                throw (0, errorHandler_1.createError)('User is already assigned to this card', 409);
            }
            const assignment = await connection_1.db
                .insert(schema_1.cardAssignments)
                .values({
                cardId,
                userId,
                assignedBy,
            })
                .returning();
            return assignment[0];
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('foreign key')) {
                throw (0, errorHandler_1.createError)('Invalid card or user reference', 400);
            }
            throw error;
        }
    }
    async removeUserFromCard(cardId, userId) {
        const result = await connection_1.db
            .delete(schema_1.cardAssignments)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.cardAssignments.cardId, cardId), (0, drizzle_orm_1.eq)(schema_1.cardAssignments.userId, userId)))
            .returning({ id: schema_1.cardAssignments.id });
        if (!result.length) {
            throw (0, errorHandler_1.createError)('Assignment not found', 404);
        }
    }
    async getCardAssignments(cardId) {
        const assignments = await connection_1.db
            .select({
            assignment: schema_1.cardAssignments,
            user: {
                id: schema_1.users.id,
                email: schema_1.users.email,
                firstName: schema_1.users.firstName,
                lastName: schema_1.users.lastName,
                avatar: schema_1.users.avatar,
            },
        })
            .from(schema_1.cardAssignments)
            .innerJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.users.id, schema_1.cardAssignments.userId))
            .where((0, drizzle_orm_1.eq)(schema_1.cardAssignments.cardId, cardId))
            .orderBy((0, drizzle_orm_1.asc)(schema_1.cardAssignments.assignedAt));
        return assignments;
    }
    async addCardComment(commentData) {
        try {
            const newComment = await connection_1.db
                .insert(schema_1.cardComments)
                .values(commentData)
                .returning();
            return newComment[0];
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('foreign key')) {
                throw (0, errorHandler_1.createError)('Invalid card or user reference', 400);
            }
            throw error;
        }
    }
    async updateCardComment(commentId, content, userId) {
        const existingComment = await connection_1.db
            .select({ userId: schema_1.cardComments.userId })
            .from(schema_1.cardComments)
            .where((0, drizzle_orm_1.eq)(schema_1.cardComments.id, commentId))
            .limit(1);
        if (!existingComment.length) {
            throw (0, errorHandler_1.createError)('Comment not found', 404);
        }
        if (existingComment[0].userId !== userId) {
            throw (0, errorHandler_1.createError)('Can only edit your own comments', 403);
        }
        const updatedComment = await connection_1.db
            .update(schema_1.cardComments)
            .set({
            content,
            isEdited: true,
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.cardComments.id, commentId))
            .returning();
        return updatedComment[0];
    }
    async deleteCardComment(commentId, userId) {
        const existingComment = await connection_1.db
            .select({ userId: schema_1.cardComments.userId })
            .from(schema_1.cardComments)
            .where((0, drizzle_orm_1.eq)(schema_1.cardComments.id, commentId))
            .limit(1);
        if (!existingComment.length) {
            throw (0, errorHandler_1.createError)('Comment not found', 404);
        }
        if (existingComment[0].userId !== userId) {
            throw (0, errorHandler_1.createError)('Can only delete your own comments', 403);
        }
        await connection_1.db
            .delete(schema_1.cardComments)
            .where((0, drizzle_orm_1.eq)(schema_1.cardComments.id, commentId));
    }
    async getCardComments(cardId) {
        const comments = await connection_1.db
            .select({
            comment: schema_1.cardComments,
            user: {
                id: schema_1.users.id,
                firstName: schema_1.users.firstName,
                lastName: schema_1.users.lastName,
                avatar: schema_1.users.avatar,
            },
        })
            .from(schema_1.cardComments)
            .innerJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.users.id, schema_1.cardComments.userId))
            .where((0, drizzle_orm_1.eq)(schema_1.cardComments.cardId, cardId))
            .orderBy((0, drizzle_orm_1.asc)(schema_1.cardComments.createdAt));
        return comments;
    }
    async createCardChecklist(checklistData) {
        try {
            if (checklistData.position === undefined) {
                const lastChecklist = await connection_1.db
                    .select({ position: schema_1.cardChecklists.position })
                    .from(schema_1.cardChecklists)
                    .where((0, drizzle_orm_1.eq)(schema_1.cardChecklists.cardId, checklistData.cardId))
                    .orderBy((0, drizzle_orm_1.desc)(schema_1.cardChecklists.position))
                    .limit(1);
                checklistData.position = lastChecklist.length > 0 ? lastChecklist[0].position + 1 : 0;
            }
            const newChecklist = await connection_1.db
                .insert(schema_1.cardChecklists)
                .values(checklistData)
                .returning();
            return newChecklist[0];
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('foreign key')) {
                throw (0, errorHandler_1.createError)('Invalid card reference', 400);
            }
            throw error;
        }
    }
    async getCardChecklists(cardId) {
        const checklists = await connection_1.db
            .select()
            .from(schema_1.cardChecklists)
            .where((0, drizzle_orm_1.eq)(schema_1.cardChecklists.cardId, cardId))
            .orderBy((0, drizzle_orm_1.asc)(schema_1.cardChecklists.position));
        const checklistsWithItems = await Promise.all(checklists.map(async (checklist) => {
            const items = await connection_1.db
                .select()
                .from(schema_1.checklistItems)
                .where((0, drizzle_orm_1.eq)(schema_1.checklistItems.checklistId, checklist.id))
                .orderBy((0, drizzle_orm_1.asc)(schema_1.checklistItems.position));
            return {
                ...checklist,
                items,
            };
        }));
        return checklistsWithItems;
    }
    async addChecklistItem(itemData) {
        try {
            if (itemData.position === undefined) {
                const lastItem = await connection_1.db
                    .select({ position: schema_1.checklistItems.position })
                    .from(schema_1.checklistItems)
                    .where((0, drizzle_orm_1.eq)(schema_1.checklistItems.checklistId, itemData.checklistId))
                    .orderBy((0, drizzle_orm_1.desc)(schema_1.checklistItems.position))
                    .limit(1);
                itemData.position = lastItem.length > 0 ? lastItem[0].position + 1 : 0;
            }
            const newItem = await connection_1.db
                .insert(schema_1.checklistItems)
                .values(itemData)
                .returning();
            return newItem[0];
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('foreign key')) {
                throw (0, errorHandler_1.createError)('Invalid checklist reference', 400);
            }
            throw error;
        }
    }
    async toggleChecklistItem(itemId, userId) {
        const existingItem = await connection_1.db
            .select()
            .from(schema_1.checklistItems)
            .where((0, drizzle_orm_1.eq)(schema_1.checklistItems.id, itemId))
            .limit(1);
        if (!existingItem.length) {
            throw (0, errorHandler_1.createError)('Checklist item not found', 404);
        }
        const item = existingItem[0];
        const isCompleted = !item.isCompleted;
        const updatedItem = await connection_1.db
            .update(schema_1.checklistItems)
            .set({
            isCompleted,
            completedBy: isCompleted ? userId : null,
            completedAt: isCompleted ? new Date() : null,
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.checklistItems.id, itemId))
            .returning();
        return updatedItem[0];
    }
}
exports.CardService = CardService;
exports.cardService = new CardService();
//# sourceMappingURL=cardService.js.map