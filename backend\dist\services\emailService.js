"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.emailService = exports.EmailService = void 0;
const nodemailer_1 = __importDefault(require("nodemailer"));
const errorHandler_1 = require("../middleware/errorHandler");
class EmailService {
    fromEmail = process.env.SMTP_FROM_EMAIL || '<EMAIL>';
    fromName = process.env.SMTP_FROM_NAME || 'Agno WorkSphere';
    transporter;
    constructor() {
        this.transporter = nodemailer_1.default.createTransport({
            host: process.env.SMTP_HOST || 'smtp.gmail.com',
            port: parseInt(process.env.SMTP_PORT || '587'),
            secure: process.env.SMTP_SECURE === 'true',
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASSWORD,
            },
        });
    }
    async sendEmail(template) {
        if (!process.env.SMTP_USER || !process.env.SMTP_PASSWORD) {
            console.warn('SMTP credentials not configured, email not sent');
            return;
        }
        try {
            const mailOptions = {
                from: `"${this.fromName}" <${this.fromEmail}>`,
                to: template.to,
                subject: template.subject,
                html: template.html,
                text: template.text || this.stripHtml(template.html),
            };
            const info = await this.transporter.sendMail(mailOptions);
            console.log(`Email sent successfully to ${template.to}. Message ID: ${info.messageId}`);
        }
        catch (error) {
            console.error('Failed to send email:', error);
            throw (0, errorHandler_1.createError)('Failed to send email', 500);
        }
    }
    generateInvitationEmail(inviteeEmail, organizationName, inviterName, role, invitationToken, tempPassword) {
        const invitationUrl = `${process.env.FRONTEND_URL}/invitation/${invitationToken}`;
        const subject = `You're invited to join ${organizationName} on Agno WorkSphere`;
        const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Invitation to ${organizationName}</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #ffffff; padding: 30px; border: 1px solid #e5e7eb; }
          .footer { background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; border: 1px solid #e5e7eb; border-top: none; }
          .button { display: inline-block; background: #3B82F6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 20px 0; }
          .button:hover { background: #2563EB; }
          .credentials { background: #f3f4f6; padding: 15px; border-radius: 6px; margin: 15px 0; }
          .role-badge { background: #dbeafe; color: #1e40af; padding: 4px 12px; border-radius: 20px; font-size: 14px; font-weight: 500; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 You're Invited!</h1>
            <p>Join ${organizationName} on Agno WorkSphere</p>
          </div>
          
          <div class="content">
            <p>Hi there!</p>
            
            <p><strong>${inviterName}</strong> has invited you to join <strong>${organizationName}</strong> as a <span class="role-badge">${role}</span> on Agno WorkSphere.</p>
            
            <p>Agno WorkSphere is a powerful project management platform that helps teams collaborate effectively with Kanban boards, task management, and real-time collaboration features.</p>
            
            ${tempPassword ? `
            <div class="credentials">
              <h3>🔐 Your Account Details</h3>
              <p><strong>Email:</strong> ${inviteeEmail}</p>
              <p><strong>Temporary Password:</strong> <code>${tempPassword}</code></p>
              <p><em>You'll be prompted to change this password when you first log in.</em></p>
            </div>
            ` : ''}
            
            <div style="text-align: center;">
              <a href="${invitationUrl}" class="button">Accept Invitation</a>
            </div>
            
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #f3f4f6; padding: 10px; border-radius: 4px; font-family: monospace;">${invitationUrl}</p>
            
            <p><strong>What you can do as a ${role}:</strong></p>
            <ul>
              ${this.getRolePermissions(role)}
            </ul>
            
            <p>This invitation will expire in 72 hours. If you have any questions, feel free to reach out to ${inviterName} or our support team.</p>
          </div>
          
          <div class="footer">
            <p>Welcome to the future of project management!</p>
            <p style="font-size: 14px; color: #6b7280;">
              This invitation was sent by ${inviterName} from ${organizationName}.<br>
              If you didn't expect this invitation, you can safely ignore this email.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
        return { to: inviteeEmail, subject, html };
    }
    generateWelcomeEmail(userEmail, userName, organizationName) {
        const subject = `Welcome to ${organizationName} on Agno WorkSphere!`;
        const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to ${organizationName}</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #10B981 0%, #059669 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #ffffff; padding: 30px; border: 1px solid #e5e7eb; }
          .footer { background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; border: 1px solid #e5e7eb; border-top: none; }
          .button { display: inline-block; background: #10B981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Welcome to the team!</h1>
            <p>You're now part of ${organizationName}</p>
          </div>
          
          <div class="content">
            <p>Hi ${userName}!</p>
            
            <p>Welcome to <strong>${organizationName}</strong> on Agno WorkSphere! We're excited to have you on board.</p>
            
            <p>Here are some things you can do to get started:</p>
            <ul>
              <li>📋 Explore your team's Kanban boards</li>
              <li>✅ Create and manage tasks</li>
              <li>👥 Collaborate with team members</li>
              <li>💬 Add comments and updates</li>
              <li>⚙️ Customize your profile settings</li>
            </ul>
            
            <div style="text-align: center;">
              <a href="${process.env.FRONTEND_URL}/dashboard" class="button">Go to Dashboard</a>
            </div>
            
            <p>If you have any questions or need help getting started, don't hesitate to reach out to your team members or our support team.</p>
          </div>
          
          <div class="footer">
            <p>Happy collaborating!</p>
            <p style="font-size: 14px; color: #6b7280;">The Agno WorkSphere Team</p>
          </div>
        </div>
      </body>
      </html>
    `;
        return { to: userEmail, subject, html };
    }
    getRolePermissions(role) {
        const permissions = {
            viewer: '<li>View boards, cards, and comments</li><li>Access team discussions</li>',
            member: '<li>View and create cards</li><li>Add comments and checklists</li><li>Collaborate on tasks</li>',
            admin: '<li>Manage boards and columns</li><li>Invite new team members</li><li>Configure board settings</li>',
            owner: '<li>Full access to organization settings</li><li>Manage all members and permissions</li><li>Access billing and subscription</li>',
        };
        return permissions[role] || permissions.member;
    }
    stripHtml(html) {
        return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
    }
}
exports.EmailService = EmailService;
exports.emailService = new EmailService();
//# sourceMappingURL=emailService.js.map