"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runMigrations = runMigrations;
const migrator_1 = require("drizzle-orm/postgres-js/migrator");
const connection_1 = require("./connection");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
async function runMigrations() {
    console.log('🔄 Running database migrations...');
    try {
        await (0, migrator_1.migrate)(connection_1.db, { migrationsFolder: './src/db/migrations' });
        console.log('✅ Migrations completed successfully');
    }
    catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    }
    finally {
        await connection_1.client.end();
    }
}
if (import.meta.url === `file://${process.argv[1]}`) {
    runMigrations();
}
//# sourceMappingURL=migrate.js.map