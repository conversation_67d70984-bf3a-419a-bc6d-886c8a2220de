"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const cardService_1 = require("../services/cardService");
const schema_1 = require("../db/schema");
const router = (0, express_1.Router)();
router.get('/board/:boardId', (0, auth_1.requirePermission)('card:read'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { boardId } = req.params;
    const includeArchived = req.query.includeArchived === 'true';
    const cardsByColumn = await cardService_1.cardService.getBoardCards(boardId, includeArchived);
    res.json({
        success: true,
        data: {
            cardsByColumn,
        },
    });
}));
router.post('/board/:boardId', (0, auth_1.requirePermission)('card:create'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { boardId } = req.params;
    const validatedData = schema_1.insertCardSchema.parse({
        ...req.body,
        boardId,
        createdBy: req.user.id,
    });
    const card = await cardService_1.cardService.createCard(validatedData, req.user.id);
    res.status(201).json({
        success: true,
        data: {
            card,
            message: 'Card created successfully',
        },
    });
}));
router.get('/:cardId', (0, auth_1.requirePermission)('card:read'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { cardId } = req.params;
    const includeDetails = req.query.includeDetails !== 'false';
    const card = await cardService_1.cardService.getCardById(cardId, includeDetails);
    res.json({
        success: true,
        data: {
            card,
        },
    });
}));
router.patch('/:cardId', (0, auth_1.requirePermission)('card:update'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { cardId } = req.params;
    const validatedData = schema_1.updateCardSchema.parse(req.body);
    const updatedCard = await cardService_1.cardService.updateCard(cardId, validatedData, req.user.id);
    res.json({
        success: true,
        data: {
            card: updatedCard,
            message: 'Card updated successfully',
        },
    });
}));
router.post('/:cardId/move', (0, auth_1.requirePermission)('card:update'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { cardId } = req.params;
    const { columnId, position } = schema_1.moveCardSchema.parse(req.body);
    const movedCard = await cardService_1.cardService.moveCard(cardId, columnId, position, req.user.id);
    res.json({
        success: true,
        data: {
            card: movedCard,
            message: 'Card moved successfully',
        },
    });
}));
router.delete('/:cardId', (0, auth_1.requirePermission)('card:delete'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { cardId } = req.params;
    await cardService_1.cardService.deleteCard(cardId, req.user.id);
    res.json({
        success: true,
        data: {
            message: 'Card deleted successfully',
        },
    });
}));
router.post('/:cardId/assign/:userId', (0, auth_1.requirePermission)('card:assign'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { cardId, userId } = req.params;
    const assignment = await cardService_1.cardService.assignUserToCard(cardId, userId, req.user.id);
    res.status(201).json({
        success: true,
        data: {
            assignment,
            message: 'User assigned to card successfully',
        },
    });
}));
router.delete('/:cardId/assign/:userId', (0, auth_1.requirePermission)('card:assign'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { cardId, userId } = req.params;
    await cardService_1.cardService.removeUserFromCard(cardId, userId);
    res.json({
        success: true,
        data: {
            message: 'User removed from card successfully',
        },
    });
}));
router.get('/:cardId/assignments', (0, auth_1.requirePermission)('card:read'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { cardId } = req.params;
    const assignments = await cardService_1.cardService.getCardAssignments(cardId);
    res.json({
        success: true,
        data: {
            assignments,
        },
    });
}));
router.post('/:cardId/comments', (0, auth_1.requirePermission)('comment:create'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { cardId } = req.params;
    const validatedData = schema_1.insertCardCommentSchema.parse({
        ...req.body,
        cardId,
        userId: req.user.id,
    });
    const comment = await cardService_1.cardService.addCardComment(validatedData);
    res.status(201).json({
        success: true,
        data: {
            comment,
            message: 'Comment added successfully',
        },
    });
}));
router.patch('/:cardId/comments/:commentId', (0, auth_1.requirePermission)('comment:update'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { commentId } = req.params;
    const { content } = schema_1.updateCardCommentSchema.parse(req.body);
    const updatedComment = await cardService_1.cardService.updateCardComment(commentId, content, req.user.id);
    res.json({
        success: true,
        data: {
            comment: updatedComment,
            message: 'Comment updated successfully',
        },
    });
}));
router.delete('/:cardId/comments/:commentId', (0, auth_1.requirePermission)('comment:delete'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { commentId } = req.params;
    await cardService_1.cardService.deleteCardComment(commentId, req.user.id);
    res.json({
        success: true,
        data: {
            message: 'Comment deleted successfully',
        },
    });
}));
router.get('/:cardId/comments', (0, auth_1.requirePermission)('comment:read'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { cardId } = req.params;
    const comments = await cardService_1.cardService.getCardComments(cardId);
    res.json({
        success: true,
        data: {
            comments,
        },
    });
}));
router.post('/:cardId/checklists', (0, auth_1.requirePermission)('card:update'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { cardId } = req.params;
    const validatedData = schema_1.insertCardChecklistSchema.parse({
        ...req.body,
        cardId,
    });
    const checklist = await cardService_1.cardService.createCardChecklist(validatedData);
    res.status(201).json({
        success: true,
        data: {
            checklist,
            message: 'Checklist created successfully',
        },
    });
}));
router.get('/:cardId/checklists', (0, auth_1.requirePermission)('card:read'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { cardId } = req.params;
    const checklists = await cardService_1.cardService.getCardChecklists(cardId);
    res.json({
        success: true,
        data: {
            checklists,
        },
    });
}));
router.post('/:cardId/checklists/:checklistId/items', (0, auth_1.requirePermission)('card:update'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { checklistId } = req.params;
    const validatedData = schema_1.insertChecklistItemSchema.parse({
        ...req.body,
        checklistId,
    });
    const item = await cardService_1.cardService.addChecklistItem(validatedData);
    res.status(201).json({
        success: true,
        data: {
            item,
            message: 'Checklist item added successfully',
        },
    });
}));
router.post('/:cardId/checklists/:checklistId/items/:itemId/toggle', (0, auth_1.requirePermission)('card:update'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw (0, errorHandler_1.createError)('Authentication required', 401);
    }
    const { itemId } = req.params;
    const updatedItem = await cardService_1.cardService.toggleChecklistItem(itemId, req.user.id);
    res.json({
        success: true,
        data: {
            item: updatedItem,
            message: 'Checklist item toggled successfully',
        },
    });
}));
exports.default = router;
//# sourceMappingURL=cards.js.map