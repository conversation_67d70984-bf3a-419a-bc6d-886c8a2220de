"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.rbacService = exports.RBACService = void 0;
const connection_1 = require("../db/connection");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const organizationMembers_1 = require("../db/schema/organizationMembers");
const errorHandler_1 = require("../middleware/errorHandler");
class RBACService {
    async hasPermission(userId, organizationId, permission) {
        try {
            const membership = await this.getUserMembership(userId, organizationId);
            if (!membership || membership.status !== 'active') {
                return false;
            }
            const allowedRoles = organizationMembers_1.PERMISSIONS[permission];
            return allowedRoles ? allowedRoles.includes(membership.role) : false;
        }
        catch {
            return false;
        }
    }
    async hasMinimumRole(userId, organizationId, minimumRole) {
        try {
            const membership = await this.getUserMembership(userId, organizationId);
            if (!membership || membership.status !== 'active') {
                return false;
            }
            const userRoleLevel = organizationMembers_1.ROLE_HIERARCHY[membership.role];
            const requiredRoleLevel = organizationMembers_1.ROLE_HIERARCHY[minimumRole];
            return userRoleLevel >= requiredRoleLevel;
        }
        catch {
            return false;
        }
    }
    async getUserMembership(userId, organizationId) {
        const membership = await connection_1.db
            .select({
            id: schema_1.organizationMembers.id,
            role: schema_1.organizationMembers.role,
            status: schema_1.organizationMembers.status,
            joinedAt: schema_1.organizationMembers.joinedAt,
        })
            .from(schema_1.organizationMembers)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.organizationMembers.userId, userId), (0, drizzle_orm_1.eq)(schema_1.organizationMembers.organizationId, organizationId)))
            .limit(1);
        return membership.length > 0 ? membership[0] : null;
    }
    async getUserMemberships(userId) {
        const memberships = await connection_1.db
            .select({
            organization: {
                id: schema_1.organizations.id,
                name: schema_1.organizations.name,
                slug: schema_1.organizations.slug,
                domain: schema_1.organizations.domain,
                isActive: schema_1.organizations.isActive,
            },
            membership: {
                id: schema_1.organizationMembers.id,
                role: schema_1.organizationMembers.role,
                status: schema_1.organizationMembers.status,
                joinedAt: schema_1.organizationMembers.joinedAt,
            },
        })
            .from(schema_1.organizationMembers)
            .innerJoin(schema_1.organizations, (0, drizzle_orm_1.eq)(schema_1.organizations.id, schema_1.organizationMembers.organizationId))
            .where((0, drizzle_orm_1.eq)(schema_1.organizationMembers.userId, userId))
            .orderBy(schema_1.organizationMembers.joinedAt);
        return memberships;
    }
    async getOrganizationMembers(organizationId, requestingUserId) {
        const hasPermission = await this.hasPermission(requestingUserId, organizationId, 'org:read');
        if (!hasPermission) {
            throw (0, errorHandler_1.createError)('Insufficient permissions to view organization members', 403);
        }
        const members = await connection_1.db
            .select({
            user: {
                id: schema_1.users.id,
                email: schema_1.users.email,
                firstName: schema_1.users.firstName,
                lastName: schema_1.users.lastName,
                avatar: schema_1.users.avatar,
                status: schema_1.users.status,
            },
            membership: {
                id: schema_1.organizationMembers.id,
                role: schema_1.organizationMembers.role,
                status: schema_1.organizationMembers.status,
                joinedAt: schema_1.organizationMembers.joinedAt,
            },
        })
            .from(schema_1.organizationMembers)
            .innerJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.users.id, schema_1.organizationMembers.userId))
            .where((0, drizzle_orm_1.eq)(schema_1.organizationMembers.organizationId, organizationId))
            .orderBy(schema_1.organizationMembers.joinedAt);
        return members;
    }
    async updateMemberRole(organizationId, targetUserId, newRole, requestingUserId) {
        const hasPermission = await this.hasPermission(requestingUserId, organizationId, 'org:manage_members');
        if (!hasPermission) {
            throw (0, errorHandler_1.createError)('Insufficient permissions to manage organization members', 403);
        }
        const requestingMembership = await this.getUserMembership(requestingUserId, organizationId);
        if (!requestingMembership) {
            throw (0, errorHandler_1.createError)('Requesting user is not a member of this organization', 403);
        }
        const targetMembership = await this.getUserMembership(targetUserId, organizationId);
        if (!targetMembership) {
            throw (0, errorHandler_1.createError)('Target user is not a member of this organization', 404);
        }
        const requestingRoleLevel = organizationMembers_1.ROLE_HIERARCHY[requestingMembership.role];
        const targetCurrentRoleLevel = organizationMembers_1.ROLE_HIERARCHY[targetMembership.role];
        const newRoleLevel = organizationMembers_1.ROLE_HIERARCHY[newRole];
        if (targetCurrentRoleLevel >= requestingRoleLevel) {
            throw (0, errorHandler_1.createError)('Cannot modify role of user with equal or higher privileges', 403);
        }
        if (newRoleLevel >= requestingRoleLevel && requestingMembership.role !== 'owner') {
            throw (0, errorHandler_1.createError)('Cannot assign role equal or higher than your own', 403);
        }
        if (targetMembership.role === 'owner' && newRole !== 'owner') {
            const ownerCount = await connection_1.db
                .select({ count: schema_1.organizationMembers.id })
                .from(schema_1.organizationMembers)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.organizationMembers.organizationId, organizationId), (0, drizzle_orm_1.eq)(schema_1.organizationMembers.role, 'owner'), (0, drizzle_orm_1.eq)(schema_1.organizationMembers.status, 'active')));
            if (ownerCount.length <= 1) {
                throw (0, errorHandler_1.createError)('Cannot remove the last owner of the organization', 400);
            }
        }
        const updatedMembership = await connection_1.db
            .update(schema_1.organizationMembers)
            .set({
            role: newRole,
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.organizationMembers.id, targetMembership.id))
            .returning();
        return updatedMembership[0];
    }
    async removeMember(organizationId, targetUserId, requestingUserId) {
        const hasPermission = await this.hasPermission(requestingUserId, organizationId, 'org:manage_members');
        if (!hasPermission) {
            throw (0, errorHandler_1.createError)('Insufficient permissions to manage organization members', 403);
        }
        const requestingMembership = await this.getUserMembership(requestingUserId, organizationId);
        const targetMembership = await this.getUserMembership(targetUserId, organizationId);
        if (!requestingMembership || !targetMembership) {
            throw (0, errorHandler_1.createError)('Invalid membership', 404);
        }
        const requestingRoleLevel = organizationMembers_1.ROLE_HIERARCHY[requestingMembership.role];
        const targetRoleLevel = organizationMembers_1.ROLE_HIERARCHY[targetMembership.role];
        if (targetRoleLevel >= requestingRoleLevel) {
            throw (0, errorHandler_1.createError)('Cannot remove user with equal or higher privileges', 403);
        }
        if (targetMembership.role === 'owner') {
            const ownerCount = await connection_1.db
                .select({ count: schema_1.organizationMembers.id })
                .from(schema_1.organizationMembers)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.organizationMembers.organizationId, organizationId), (0, drizzle_orm_1.eq)(schema_1.organizationMembers.role, 'owner'), (0, drizzle_orm_1.eq)(schema_1.organizationMembers.status, 'active')));
            if (ownerCount.length <= 1) {
                throw (0, errorHandler_1.createError)('Cannot remove the last owner of the organization', 400);
            }
        }
        await connection_1.db
            .delete(schema_1.organizationMembers)
            .where((0, drizzle_orm_1.eq)(schema_1.organizationMembers.id, targetMembership.id));
        return { success: true };
    }
    async getUserPermissions(userId, organizationId) {
        const membership = await this.getUserMembership(userId, organizationId);
        if (!membership || membership.status !== 'active') {
            return [];
        }
        const userPermissions = [];
        for (const [permission, allowedRoles] of Object.entries(organizationMembers_1.PERMISSIONS)) {
            if (allowedRoles.includes(membership.role)) {
                userPermissions.push(permission);
            }
        }
        return userPermissions;
    }
}
exports.RBACService = RBACService;
exports.rbacService = new RBACService();
//# sourceMappingURL=rbacService.js.map