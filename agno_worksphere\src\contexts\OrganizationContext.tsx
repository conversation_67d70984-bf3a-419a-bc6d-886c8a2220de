import React, { createContext, useContext, useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useMyOrganizations, useOrganization } from '../hooks/useOrganizations';
import type { Organization, OrganizationMembership } from '../types/api';

interface OrganizationContextType {
  currentOrganization: Organization | null;
  currentMembership: { role: string; status: string } | null;
  organizations: OrganizationMembership[];
  isLoading: boolean;
  error: string | null;
  switchOrganization: (organizationId: string) => void;
  refreshOrganizations: () => void;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

export function OrganizationProvider({ children }: { children: React.ReactNode }) {
  const { organizationId } = useParams<{ organizationId: string }>();
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);

  // Fetch user's organizations
  const {
    data: myOrgsData,
    isLoading: isLoadingOrgs,
    error: orgsError,
    refetch: refetchOrganizations,
  } = useMyOrganizations();

  // Fetch current organization details
  const {
    data: currentOrgData,
    isLoading: isLoadingCurrentOrg,
    error: currentOrgError,
  } = useOrganization(organizationId || '');

  const organizations = myOrgsData?.organizations || [];
  const currentOrganization = currentOrgData?.organization || null;
  const currentMembership = currentOrgData?.membership || null;

  // Handle organization switching
  const switchOrganization = (newOrganizationId: string) => {
    if (newOrganizationId !== organizationId) {
      navigate(`/org/${newOrganizationId}/dashboard`);
    }
  };

  // Auto-redirect to first organization if no organization is selected
  useEffect(() => {
    if (!organizationId && organizations.length > 0 && !isLoadingOrgs) {
      const firstOrg = organizations[0];
      if (firstOrg?.organization?.id) {
        navigate(`/org/${firstOrg.organization.id}/dashboard`, { replace: true });
      }
    }
  }, [organizationId, organizations, isLoadingOrgs, navigate]);

  // Handle errors
  useEffect(() => {
    if (orgsError) {
      setError('Failed to load organizations');
    } else if (currentOrgError) {
      setError('Failed to load organization details');
    } else {
      setError(null);
    }
  }, [orgsError, currentOrgError]);

  // Check if user has access to the requested organization
  useEffect(() => {
    if (organizationId && organizations.length > 0 && !isLoadingOrgs) {
      const hasAccess = organizations.some(
        (org) => org.organization.id === organizationId
      );
      
      if (!hasAccess) {
        setError('You do not have access to this organization');
        // Redirect to first available organization
        const firstOrg = organizations[0];
        if (firstOrg?.organization?.id) {
          navigate(`/org/${firstOrg.organization.id}/dashboard`, { replace: true });
        }
      }
    }
  }, [organizationId, organizations, isLoadingOrgs, navigate]);

  const refreshOrganizations = () => {
    refetchOrganizations();
  };

  const value: OrganizationContextType = {
    currentOrganization,
    currentMembership,
    organizations,
    isLoading: isLoadingOrgs || isLoadingCurrentOrg,
    error,
    switchOrganization,
    refreshOrganizations,
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
}

export const useOrganizationContext = () => {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganizationContext must be used within an OrganizationProvider');
  }
  return context;
};

export default OrganizationContext;
