import axios, { AxiosInstance, AxiosResponse } from 'axios';
import type { ApiResponse } from '../types/api';

class ApiService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api',
      timeout: 30000,
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add JWT token if available
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      (error) => {
        // Handle common errors
        if (error.response?.status === 401) {
          // Handle unauthorized - redirect to login
          window.location.href = '/login';
        }
        
        return Promise.reject(error);
      }
    );
  }

  // Generic request methods
  async get<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    const response = await this.client.get<ApiResponse<T>>(url, { params });
    return response.data;
  }

  async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.client.post<ApiResponse<T>>(url, data);
    return response.data;
  }

  async patch<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.client.patch<ApiResponse<T>>(url, data);
    return response.data;
  }

  async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.client.put<ApiResponse<T>>(url, data);
    return response.data;
  }

  async delete<T>(url: string): Promise<ApiResponse<T>> {
    const response = await this.client.delete<ApiResponse<T>>(url);
    return response.data;
  }

  // Auth endpoints
  auth = {
    login: (data: { email: string; password: string }) =>
      this.post('/auth/login', data),

    register: (data: any) =>
      this.post('/auth/register', data),

    logout: () =>
      this.post('/auth/logout'),

    me: () =>
      this.get('/auth/me'),

    updateProfile: (data: any) =>
      this.patch('/users/profile', data),

    changePassword: (data: { currentPassword: string; newPassword: string; confirmPassword: string }) =>
      this.post('/auth/change-password', data),
  };

  // Organization endpoints
  organizations = {
    create: (data: any) =>
      this.post('/organizations', data),
    
    getMyOrganizations: (query?: string) =>
      this.get('/organizations/my-organizations', { q: query }),
    
    switch: (organizationId: string) =>
      this.post(`/organizations/switch/${organizationId}`),
    
    getById: (organizationId: string) =>
      this.get(`/organizations/${organizationId}`),
    
    update: (organizationId: string, data: any) =>
      this.patch(`/organizations/${organizationId}`, data),
    
    delete: (organizationId: string) =>
      this.delete(`/organizations/${organizationId}`),
    
    getMembers: (organizationId: string) =>
      this.get(`/organizations/${organizationId}/members`),
    
    updateMemberRole: (organizationId: string, memberId: string, data: { role: string }) =>
      this.put(`/organizations/${organizationId}/members/${memberId}`, data),

    removeMember: (organizationId: string, memberId: string) =>
      this.delete(`/organizations/${organizationId}/members/${memberId}`),

    getStats: (organizationId: string) =>
      this.get(`/organizations/${organizationId}/stats`),

    bulkUpdateMemberRoles: (organizationId: string, data: { memberIds: string[]; role: string }) =>
      this.put(`/organizations/${organizationId}/members/bulk-update`, data),
  };

  // Board endpoints
  boards = {
    getOrganizationBoards: (organizationId: string, includeArchived?: boolean) =>
      this.get(`/boards/organization/${organizationId}`, { includeArchived }),
    
    create: (organizationId: string, data: any) =>
      this.post(`/boards/organization/${organizationId}`, data),
    
    getById: (boardId: string, includeColumns?: boolean) =>
      this.get(`/boards/${boardId}`, { includeColumns }),
    
    update: (boardId: string, data: any) =>
      this.patch(`/boards/${boardId}`, data),
    
    delete: (boardId: string) =>
      this.delete(`/boards/${boardId}`),
    
    getColumns: (boardId: string) =>
      this.get(`/boards/${boardId}/columns`),
    
    createColumn: (boardId: string, data: any) =>
      this.post(`/boards/${boardId}/columns`, data),
    
    updateColumn: (boardId: string, columnId: string, data: any) =>
      this.patch(`/boards/${boardId}/columns/${columnId}`, data),
    
    deleteColumn: (boardId: string, columnId: string) =>
      this.delete(`/boards/${boardId}/columns/${columnId}`),
    
    reorderColumns: (boardId: string, columnIds: string[]) =>
      this.post(`/boards/${boardId}/columns/reorder`, { columnIds }),

    // Board member management
    getMembers: (boardId: string) =>
      this.get(`/boards/${boardId}/members`),

    addMember: (boardId: string, data: { userId: string; role?: string }) =>
      this.post(`/boards/${boardId}/members`, data),

    updateMemberRole: (boardId: string, memberId: string, data: { role: string }) =>
      this.put(`/boards/${boardId}/members/${memberId}`, data),

    removeMember: (boardId: string, memberId: string) =>
      this.delete(`/boards/${boardId}/members/${memberId}`),
  };

  // Card endpoints
  cards = {
    getBoardCards: (boardId: string, includeArchived?: boolean) =>
      this.get(`/cards/board/${boardId}`, { includeArchived }),
    
    create: (boardId: string, data: any) =>
      this.post(`/cards/board/${boardId}`, data),
    
    getById: (cardId: string, includeDetails?: boolean) =>
      this.get(`/cards/${cardId}`, { includeDetails }),
    
    update: (cardId: string, data: any) =>
      this.patch(`/cards/${cardId}`, data),
    
    move: (cardId: string, data: { columnId: string; position: number }) =>
      this.post(`/cards/${cardId}/move`, data),
    
    delete: (cardId: string) =>
      this.delete(`/cards/${cardId}`),
    
    assignUser: (cardId: string, userId: string) =>
      this.post(`/cards/${cardId}/assign/${userId}`),
    
    removeUser: (cardId: string, userId: string) =>
      this.delete(`/cards/${cardId}/assign/${userId}`),
    
    getAssignments: (cardId: string) =>
      this.get(`/cards/${cardId}/assignments`),
    
    addComment: (cardId: string, data: { content: string }) =>
      this.post(`/cards/${cardId}/comments`, data),
    
    updateComment: (cardId: string, commentId: string, data: { content: string }) =>
      this.patch(`/cards/${cardId}/comments/${commentId}`, data),
    
    deleteComment: (cardId: string, commentId: string) =>
      this.delete(`/cards/${cardId}/comments/${commentId}`),
    
    getComments: (cardId: string) =>
      this.get(`/cards/${cardId}/comments`),
    
    createChecklist: (cardId: string, data: { title: string; position?: number }) =>
      this.post(`/cards/${cardId}/checklists`, data),
    
    getChecklists: (cardId: string) =>
      this.get(`/cards/${cardId}/checklists`),
    
    addChecklistItem: (cardId: string, checklistId: string, data: { text: string; position?: number }) =>
      this.post(`/cards/${cardId}/checklists/${checklistId}/items`, data),
    
    toggleChecklistItem: (cardId: string, checklistId: string, itemId: string) =>
      this.post(`/cards/${cardId}/checklists/${checklistId}/items/${itemId}/toggle`),
  };

  // Invitation endpoints
  invitations = {
    getByToken: (token: string) =>
      this.get(`/invitations/${token}`),

    accept: (data: any) =>
      this.post('/invitations/accept', data),

    decline: (token: string) =>
      this.post(`/invitations/decline/${token}`),

    send: (organizationId: string, data: { email: string; role: string }) =>
      this.post(`/invitations/organization/${organizationId}/invite`, data),

    getOrganizationInvitations: (organizationId: string) =>
      this.get(`/invitations/organization/${organizationId}`),

    cancel: (invitationId: string) =>
      this.delete(`/invitations/${invitationId}`),
  };

  // User endpoints
  users = {
    getProfile: () =>
      this.get('/users/profile'),

    updateProfile: (data: any) =>
      this.put('/users/profile', data),

    getById: (userId: string) =>
      this.get(`/users/${userId}`),

    search: (query: string) =>
      this.get('/users/search', { q: query }),
  };
}

export const apiService = new ApiService();
export default apiService;
