"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.resendInvitationSchema = exports.acceptInvitationSchema = exports.selectInvitationSchema = exports.insertInvitationSchema = exports.invitations = exports.invitationStatusEnum = void 0;
const pg_core_1 = require("drizzle-orm/pg-core");
const drizzle_zod_1 = require("drizzle-zod");
const zod_1 = require("zod");
const organizations_1 = require("./organizations");
const users_1 = require("./users");
const organizationMembers_1 = require("./organizationMembers");
exports.invitationStatusEnum = (0, pg_core_1.pgEnum)('invitation_status', ['pending', 'accepted', 'declined', 'expired']);
exports.invitations = (0, pg_core_1.pgTable)('invitations', {
    id: (0, pg_core_1.uuid)('id').primaryKey().defaultRandom(),
    organizationId: (0, pg_core_1.uuid)('organization_id').notNull(),
    email: (0, pg_core_1.varchar)('email', { length: 255 }).notNull(),
    role: (0, organizationMembers_1.roleEnum)('role').notNull().default('member'),
    token: (0, pg_core_1.varchar)('token', { length: 255 }).notNull().unique(),
    tempPassword: (0, pg_core_1.varchar)('temp_password', { length: 255 }),
    status: (0, exports.invitationStatusEnum)('status').notNull().default('pending'),
    invitedBy: (0, pg_core_1.uuid)('invited_by').notNull(),
    acceptedBy: (0, pg_core_1.uuid)('accepted_by'),
    expiresAt: (0, pg_core_1.timestamp)('expires_at').notNull(),
    acceptedAt: (0, pg_core_1.timestamp)('accepted_at'),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow().notNull(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').defaultNow().notNull(),
}, (table) => ({
    organizationFk: (0, pg_core_1.foreignKey)({
        columns: [table.organizationId],
        foreignColumns: [organizations_1.organizations.id],
        name: 'invitations_organization_id_fk'
    }).onDelete('cascade'),
    invitedByFk: (0, pg_core_1.foreignKey)({
        columns: [table.invitedBy],
        foreignColumns: [users_1.users.id],
        name: 'invitations_invited_by_fk'
    }).onDelete('restrict'),
    acceptedByFk: (0, pg_core_1.foreignKey)({
        columns: [table.acceptedBy],
        foreignColumns: [users_1.users.id],
        name: 'invitations_accepted_by_fk'
    }).onDelete('set null'),
}));
exports.insertInvitationSchema = (0, drizzle_zod_1.createInsertSchema)(exports.invitations, {
    organizationId: zod_1.z.string().uuid('Invalid organization ID'),
    email: zod_1.z.string().email('Invalid email format'),
    role: zod_1.z.enum(['viewer', 'member', 'admin', 'owner']),
    invitedBy: zod_1.z.string().uuid('Invalid user ID'),
});
exports.selectInvitationSchema = (0, drizzle_zod_1.createSelectSchema)(exports.invitations);
exports.acceptInvitationSchema = zod_1.z.object({
    token: zod_1.z.string().min(1, 'Token is required'),
    firstName: zod_1.z.string().min(1, 'First name is required').max(100),
    lastName: zod_1.z.string().min(1, 'Last name is required').max(100),
    password: zod_1.z.string().min(8, 'Password must be at least 8 characters'),
    confirmPassword: zod_1.z.string(),
}).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
});
exports.resendInvitationSchema = zod_1.z.object({
    invitationId: zod_1.z.string().uuid('Invalid invitation ID'),
});
//# sourceMappingURL=invitations.js.map