import { type Role, type Permission } from '../db/schema/organizationMembers';
export declare class RBACService {
    hasPermission(userId: string, organizationId: string, permission: Permission): Promise<boolean>;
    hasMinimumRole(userId: string, organizationId: string, minimumRole: Role): Promise<boolean>;
    getUserMembership(userId: string, organizationId: string): Promise<{
        id: string;
        role: "viewer" | "member" | "admin" | "owner";
        status: "active" | "inactive" | "pending";
        joinedAt: Date;
    } | null | undefined>;
    getUserMemberships(userId: string): Promise<{
        organization: {
            id: string;
            name: string;
            slug: string;
            domain: string | null;
            isActive: boolean;
        };
        membership: {
            id: string;
            role: "viewer" | "member" | "admin" | "owner";
            status: "active" | "inactive" | "pending";
            joinedAt: Date;
        };
    }[]>;
    getOrganizationMembers(organizationId: string, requestingUserId: string): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            avatar: string | null;
            status: "active" | "inactive" | "pending" | "suspended";
        };
        membership: {
            id: string;
            role: "viewer" | "member" | "admin" | "owner";
            status: "active" | "inactive" | "pending";
            joinedAt: Date;
        };
    }[]>;
    updateMemberRole(organizationId: string, targetUserId: string, newRole: Role, requestingUserId: string): Promise<{
        id: string;
        organizationId: string;
        userId: string;
        role: "viewer" | "member" | "admin" | "owner";
        status: "active" | "inactive" | "pending";
        invitedBy: string | null;
        joinedAt: Date;
        createdAt: Date;
        updatedAt: Date;
    } | undefined>;
    removeMember(organizationId: string, targetUserId: string, requestingUserId: string): Promise<{
        success: boolean;
    }>;
    getUserPermissions(userId: string, organizationId: string): Promise<Permission[]>;
}
export declare const rbacService: RBACService;
//# sourceMappingURL=rbacService.d.ts.map