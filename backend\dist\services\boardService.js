"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.boardService = exports.BoardService = void 0;
const connection_1 = require("../db/connection");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const errorHandler_1 = require("../middleware/errorHandler");
class BoardService {
    async createBoard(boardData, createdBy) {
        try {
            const organization = await connection_1.db
                .select({ id: schema_1.organizations.id, isActive: schema_1.organizations.isActive })
                .from(schema_1.organizations)
                .where((0, drizzle_orm_1.eq)(schema_1.organizations.id, boardData.organizationId))
                .limit(1);
            if (!organization.length || !organization[0].isActive) {
                throw (0, errorHandler_1.createError)('Organization not found or inactive', 404);
            }
            const newBoard = await connection_1.db
                .insert(schema_1.boards)
                .values({
                ...boardData,
                createdBy,
                settings: boardData.settings || {
                    allowComments: true,
                    allowAttachments: true,
                    cardCoverEnabled: true,
                    dueDateReminders: true,
                    emailNotifications: true,
                    visibility: 'organization',
                },
            })
                .returning();
            const board = newBoard[0];
            const defaultColumns = [
                { name: 'To Do', position: 0, color: '#6B7280' },
                { name: 'In Progress', position: 1, color: '#3B82F6' },
                { name: 'Done', position: 2, color: '#10B981' },
            ];
            await connection_1.db.insert(schema_1.boardColumns).values(defaultColumns.map(col => ({
                boardId: board.id,
                name: col.name,
                position: col.position,
                color: col.color,
            })));
            return board;
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('foreign key')) {
                throw (0, errorHandler_1.createError)('Invalid organization reference', 400);
            }
            throw error;
        }
    }
    async getBoardById(boardId, includeColumns = true) {
        const board = await connection_1.db
            .select()
            .from(schema_1.boards)
            .where((0, drizzle_orm_1.eq)(schema_1.boards.id, boardId))
            .limit(1);
        if (!board.length) {
            throw (0, errorHandler_1.createError)('Board not found', 404);
        }
        const boardData = board[0];
        if (includeColumns) {
            const columns = await this.getBoardColumns(boardId);
            return {
                ...boardData,
                columns,
            };
        }
        return boardData;
    }
    async getOrganizationBoards(organizationId, includeArchived = false) {
        let whereCondition = (0, drizzle_orm_1.eq)(schema_1.boards.organizationId, organizationId);
        if (!includeArchived) {
            whereCondition = (0, drizzle_orm_1.and)(whereCondition, (0, drizzle_orm_1.eq)(schema_1.boards.isArchived, false));
        }
        const organizationBoards = await connection_1.db
            .select()
            .from(schema_1.boards)
            .where(whereCondition)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.boards.createdAt));
        return organizationBoards;
    }
    async updateBoard(boardId, updates, userId) {
        const existingBoard = await connection_1.db
            .select()
            .from(schema_1.boards)
            .where((0, drizzle_orm_1.eq)(schema_1.boards.id, boardId))
            .limit(1);
        if (!existingBoard.length) {
            throw (0, errorHandler_1.createError)('Board not found', 404);
        }
        const updatedBoard = await connection_1.db
            .update(schema_1.boards)
            .set({
            ...updates,
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.boards.id, boardId))
            .returning();
        if (!updatedBoard.length) {
            throw (0, errorHandler_1.createError)('Failed to update board', 500);
        }
        return updatedBoard[0];
    }
    async deleteBoard(boardId, userId) {
        const result = await connection_1.db
            .update(schema_1.boards)
            .set({
            isArchived: true,
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.boards.id, boardId))
            .returning({ id: schema_1.boards.id });
        if (!result.length) {
            throw (0, errorHandler_1.createError)('Board not found', 404);
        }
    }
    async getBoardColumns(boardId) {
        const columns = await connection_1.db
            .select()
            .from(schema_1.boardColumns)
            .where((0, drizzle_orm_1.eq)(schema_1.boardColumns.boardId, boardId))
            .orderBy((0, drizzle_orm_1.asc)(schema_1.boardColumns.position));
        return columns;
    }
    async createBoardColumn(columnData) {
        try {
            const board = await connection_1.db
                .select({ id: schema_1.boards.id })
                .from(schema_1.boards)
                .where((0, drizzle_orm_1.eq)(schema_1.boards.id, columnData.boardId))
                .limit(1);
            if (!board.length) {
                throw (0, errorHandler_1.createError)('Board not found', 404);
            }
            if (columnData.position === undefined) {
                const lastColumn = await connection_1.db
                    .select({ position: schema_1.boardColumns.position })
                    .from(schema_1.boardColumns)
                    .where((0, drizzle_orm_1.eq)(schema_1.boardColumns.boardId, columnData.boardId))
                    .orderBy((0, drizzle_orm_1.desc)(schema_1.boardColumns.position))
                    .limit(1);
                columnData.position = lastColumn.length > 0 ? lastColumn[0].position + 1 : 0;
            }
            const newColumn = await connection_1.db
                .insert(schema_1.boardColumns)
                .values(columnData)
                .returning();
            return newColumn[0];
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('foreign key')) {
                throw (0, errorHandler_1.createError)('Invalid board reference', 400);
            }
            throw error;
        }
    }
    async updateBoardColumn(columnId, updates) {
        const updatedColumn = await connection_1.db
            .update(schema_1.boardColumns)
            .set({
            ...updates,
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.boardColumns.id, columnId))
            .returning();
        if (!updatedColumn.length) {
            throw (0, errorHandler_1.createError)('Column not found', 404);
        }
        return updatedColumn[0];
    }
    async deleteBoardColumn(columnId) {
        const cardsInColumn = await connection_1.db
            .select({ id: schema_1.cards.id })
            .from(schema_1.cards)
            .where((0, drizzle_orm_1.eq)(schema_1.cards.columnId, columnId))
            .limit(1);
        if (cardsInColumn.length > 0) {
            throw (0, errorHandler_1.createError)('Cannot delete column with cards. Move cards first.', 400);
        }
        const result = await connection_1.db
            .delete(schema_1.boardColumns)
            .where((0, drizzle_orm_1.eq)(schema_1.boardColumns.id, columnId))
            .returning({ id: schema_1.boardColumns.id });
        if (!result.length) {
            throw (0, errorHandler_1.createError)('Column not found', 404);
        }
    }
    async reorderColumns(boardId, columnIds) {
        const existingColumns = await connection_1.db
            .select({ id: schema_1.boardColumns.id })
            .from(schema_1.boardColumns)
            .where((0, drizzle_orm_1.eq)(schema_1.boardColumns.boardId, boardId));
        const existingColumnIds = existingColumns.map(col => col.id);
        const invalidColumns = columnIds.filter(id => !existingColumnIds.includes(id));
        if (invalidColumns.length > 0) {
            throw (0, errorHandler_1.createError)('Invalid column IDs provided', 400);
        }
        if (columnIds.length !== existingColumnIds.length) {
            throw (0, errorHandler_1.createError)('All columns must be included in reorder', 400);
        }
        const updatePromises = columnIds.map((columnId, index) => connection_1.db
            .update(schema_1.boardColumns)
            .set({
            position: index,
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.boardColumns.id, columnId)));
        await Promise.all(updatePromises);
        return this.getBoardColumns(boardId);
    }
    async getBoardStats(boardId) {
        const columnCount = await connection_1.db
            .select({ count: schema_1.boardColumns.id })
            .from(schema_1.boardColumns)
            .where((0, drizzle_orm_1.eq)(schema_1.boardColumns.boardId, boardId));
        const cardCount = await connection_1.db
            .select({ count: schema_1.cards.id })
            .from(schema_1.cards)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.cards.boardId, boardId), (0, drizzle_orm_1.eq)(schema_1.cards.isArchived, false)));
        return {
            columns: columnCount.length,
            cards: cardCount.length,
        };
    }
    async canUserAccessBoard(boardId, userId) {
        const board = await connection_1.db
            .select({
            organizationId: schema_1.boards.organizationId,
            settings: schema_1.boards.settings,
        })
            .from(schema_1.boards)
            .where((0, drizzle_orm_1.eq)(schema_1.boards.id, boardId))
            .limit(1);
        if (!board.length) {
            return false;
        }
        return true;
    }
}
exports.BoardService = BoardService;
exports.boardService = new BoardService();
//# sourceMappingURL=boardService.js.map