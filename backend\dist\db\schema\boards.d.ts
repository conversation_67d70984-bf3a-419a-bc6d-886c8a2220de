import { z } from 'zod';
export declare const boards: import("drizzle-orm/pg-core").PgTableWithColumns<{
    name: "boards";
    schema: undefined;
    columns: {
        id: import("drizzle-orm/pg-core").PgColumn<{
            name: "id";
            tableName: "boards";
            dataType: "string";
            columnType: "PgUUID";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: true;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        organizationId: import("drizzle-orm/pg-core").PgColumn<{
            name: "organization_id";
            tableName: "boards";
            dataType: "string";
            columnType: "PgUUID";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        name: import("drizzle-orm/pg-core").PgColumn<{
            name: "name";
            tableName: "boards";
            dataType: "string";
            columnType: "PgVarchar";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: 255;
        }>;
        description: import("drizzle-orm/pg-core").PgColumn<{
            name: "description";
            tableName: "boards";
            dataType: "string";
            columnType: "PgText";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        color: import("drizzle-orm/pg-core").PgColumn<{
            name: "color";
            tableName: "boards";
            dataType: "string";
            columnType: "PgVarchar";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: 7;
        }>;
        isTemplate: import("drizzle-orm/pg-core").PgColumn<{
            name: "is_template";
            tableName: "boards";
            dataType: "boolean";
            columnType: "PgBoolean";
            data: boolean;
            driverParam: boolean;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        isArchived: import("drizzle-orm/pg-core").PgColumn<{
            name: "is_archived";
            tableName: "boards";
            dataType: "boolean";
            columnType: "PgBoolean";
            data: boolean;
            driverParam: boolean;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        settings: import("drizzle-orm/pg-core").PgColumn<{
            name: "settings";
            tableName: "boards";
            dataType: "json";
            columnType: "PgJsonb";
            data: {
                allowComments?: boolean;
                allowAttachments?: boolean;
                cardCoverEnabled?: boolean;
                dueDateReminders?: boolean;
                emailNotifications?: boolean;
                visibility?: "private" | "organization" | "public";
            };
            driverParam: unknown;
            notNull: false;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            $type: {
                allowComments?: boolean;
                allowAttachments?: boolean;
                cardCoverEnabled?: boolean;
                dueDateReminders?: boolean;
                emailNotifications?: boolean;
                visibility?: "private" | "organization" | "public";
            };
        }>;
        createdBy: import("drizzle-orm/pg-core").PgColumn<{
            name: "created_by";
            tableName: "boards";
            dataType: "string";
            columnType: "PgUUID";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        createdAt: import("drizzle-orm/pg-core").PgColumn<{
            name: "created_at";
            tableName: "boards";
            dataType: "date";
            columnType: "PgTimestamp";
            data: Date;
            driverParam: string;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        updatedAt: import("drizzle-orm/pg-core").PgColumn<{
            name: "updated_at";
            tableName: "boards";
            dataType: "date";
            columnType: "PgTimestamp";
            data: Date;
            driverParam: string;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
    };
    dialect: "pg";
}>;
export declare const boardColumns: import("drizzle-orm/pg-core").PgTableWithColumns<{
    name: "board_columns";
    schema: undefined;
    columns: {
        id: import("drizzle-orm/pg-core").PgColumn<{
            name: "id";
            tableName: "board_columns";
            dataType: "string";
            columnType: "PgUUID";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: true;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        boardId: import("drizzle-orm/pg-core").PgColumn<{
            name: "board_id";
            tableName: "board_columns";
            dataType: "string";
            columnType: "PgUUID";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        name: import("drizzle-orm/pg-core").PgColumn<{
            name: "name";
            tableName: "board_columns";
            dataType: "string";
            columnType: "PgVarchar";
            data: string;
            driverParam: string;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: 255;
        }>;
        position: import("drizzle-orm/pg-core").PgColumn<{
            name: "position";
            tableName: "board_columns";
            dataType: "number";
            columnType: "PgInteger";
            data: number;
            driverParam: string | number;
            notNull: true;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        color: import("drizzle-orm/pg-core").PgColumn<{
            name: "color";
            tableName: "board_columns";
            dataType: "string";
            columnType: "PgVarchar";
            data: string;
            driverParam: string;
            notNull: false;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: [string, ...string[]];
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {
            length: 7;
        }>;
        isCollapsed: import("drizzle-orm/pg-core").PgColumn<{
            name: "is_collapsed";
            tableName: "board_columns";
            dataType: "boolean";
            columnType: "PgBoolean";
            data: boolean;
            driverParam: boolean;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        cardLimit: import("drizzle-orm/pg-core").PgColumn<{
            name: "card_limit";
            tableName: "board_columns";
            dataType: "number";
            columnType: "PgInteger";
            data: number;
            driverParam: string | number;
            notNull: false;
            hasDefault: false;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        createdAt: import("drizzle-orm/pg-core").PgColumn<{
            name: "created_at";
            tableName: "board_columns";
            dataType: "date";
            columnType: "PgTimestamp";
            data: Date;
            driverParam: string;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
        updatedAt: import("drizzle-orm/pg-core").PgColumn<{
            name: "updated_at";
            tableName: "board_columns";
            dataType: "date";
            columnType: "PgTimestamp";
            data: Date;
            driverParam: string;
            notNull: true;
            hasDefault: true;
            isPrimaryKey: false;
            isAutoincrement: false;
            hasRuntimeDefault: false;
            enumValues: undefined;
            baseColumn: never;
            identity: undefined;
            generated: undefined;
        }, {}, {}>;
    };
    dialect: "pg";
}>;
export declare const insertBoardSchema: z.ZodObject<{
    organizationId: z.ZodString;
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    color: z.ZodOptional<z.ZodString>;
    isTemplate: z.ZodDefault<z.ZodBoolean>;
    isArchived: z.ZodDefault<z.ZodBoolean>;
    settings: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    createdBy: z.ZodString;
}, "strip", z.ZodTypeAny, {
    name: string;
    organizationId: string;
    isTemplate: boolean;
    isArchived: boolean;
    createdBy: string;
    description?: string | undefined;
    settings?: Record<string, any> | undefined;
    color?: string | undefined;
}, {
    name: string;
    organizationId: string;
    createdBy: string;
    description?: string | undefined;
    settings?: Record<string, any> | undefined;
    color?: string | undefined;
    isTemplate?: boolean | undefined;
    isArchived?: boolean | undefined;
}>;
export declare const selectBoardSchema: z.ZodObject<{
    id: z.ZodString;
    organizationId: z.ZodString;
    name: z.ZodString;
    description: z.ZodNullable<z.ZodString>;
    color: z.ZodNullable<z.ZodString>;
    isTemplate: z.ZodBoolean;
    isArchived: z.ZodBoolean;
    settings: z.ZodNullable<z.ZodRecord<z.ZodString, z.ZodAny>>;
    createdBy: z.ZodString;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: string;
    description: string | null;
    settings: Record<string, any> | null;
    createdAt: Date;
    updatedAt: Date;
    organizationId: string;
    color: string | null;
    isTemplate: boolean;
    isArchived: boolean;
    createdBy: string;
}, {
    id: string;
    name: string;
    description: string | null;
    settings: Record<string, any> | null;
    createdAt: Date;
    updatedAt: Date;
    organizationId: string;
    color: string | null;
    isTemplate: boolean;
    isArchived: boolean;
    createdBy: string;
}>;
export declare const updateBoardSchema: z.ZodObject<Omit<{
    organizationId: z.ZodOptional<z.ZodString>;
    name: z.ZodOptional<z.ZodString>;
    description: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    color: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    isTemplate: z.ZodOptional<z.ZodDefault<z.ZodBoolean>>;
    isArchived: z.ZodOptional<z.ZodDefault<z.ZodBoolean>>;
    settings: z.ZodOptional<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
    createdBy: z.ZodOptional<z.ZodString>;
}, "organizationId" | "createdBy">, "strip", z.ZodTypeAny, {
    name?: string | undefined;
    description?: string | undefined;
    settings?: Record<string, any> | undefined;
    color?: string | undefined;
    isTemplate?: boolean | undefined;
    isArchived?: boolean | undefined;
}, {
    name?: string | undefined;
    description?: string | undefined;
    settings?: Record<string, any> | undefined;
    color?: string | undefined;
    isTemplate?: boolean | undefined;
    isArchived?: boolean | undefined;
}>;
export declare const insertBoardColumnSchema: z.ZodObject<{
    boardId: z.ZodString;
    name: z.ZodString;
    position: z.ZodNumber;
    color: z.ZodOptional<z.ZodString>;
    isCollapsed: z.ZodDefault<z.ZodBoolean>;
    cardLimit: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    name: string;
    boardId: string;
    position: number;
    isCollapsed: boolean;
    color?: string | undefined;
    cardLimit?: number | undefined;
}, {
    name: string;
    boardId: string;
    position: number;
    color?: string | undefined;
    isCollapsed?: boolean | undefined;
    cardLimit?: number | undefined;
}>;
export declare const selectBoardColumnSchema: import("drizzle-zod").BuildSchema<"select", {
    id: import("drizzle-orm/pg-core").PgColumn<{
        name: "id";
        tableName: "board_columns";
        dataType: "string";
        columnType: "PgUUID";
        data: string;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: true;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    boardId: import("drizzle-orm/pg-core").PgColumn<{
        name: "board_id";
        tableName: "board_columns";
        dataType: "string";
        columnType: "PgUUID";
        data: string;
        driverParam: string;
        notNull: true;
        hasDefault: false;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    name: import("drizzle-orm/pg-core").PgColumn<{
        name: "name";
        tableName: "board_columns";
        dataType: "string";
        columnType: "PgVarchar";
        data: string;
        driverParam: string;
        notNull: true;
        hasDefault: false;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: [string, ...string[]];
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {
        length: 255;
    }>;
    position: import("drizzle-orm/pg-core").PgColumn<{
        name: "position";
        tableName: "board_columns";
        dataType: "number";
        columnType: "PgInteger";
        data: number;
        driverParam: string | number;
        notNull: true;
        hasDefault: false;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    color: import("drizzle-orm/pg-core").PgColumn<{
        name: "color";
        tableName: "board_columns";
        dataType: "string";
        columnType: "PgVarchar";
        data: string;
        driverParam: string;
        notNull: false;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: [string, ...string[]];
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {
        length: 7;
    }>;
    isCollapsed: import("drizzle-orm/pg-core").PgColumn<{
        name: "is_collapsed";
        tableName: "board_columns";
        dataType: "boolean";
        columnType: "PgBoolean";
        data: boolean;
        driverParam: boolean;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    cardLimit: import("drizzle-orm/pg-core").PgColumn<{
        name: "card_limit";
        tableName: "board_columns";
        dataType: "number";
        columnType: "PgInteger";
        data: number;
        driverParam: string | number;
        notNull: false;
        hasDefault: false;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    createdAt: import("drizzle-orm/pg-core").PgColumn<{
        name: "created_at";
        tableName: "board_columns";
        dataType: "date";
        columnType: "PgTimestamp";
        data: Date;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
    updatedAt: import("drizzle-orm/pg-core").PgColumn<{
        name: "updated_at";
        tableName: "board_columns";
        dataType: "date";
        columnType: "PgTimestamp";
        data: Date;
        driverParam: string;
        notNull: true;
        hasDefault: true;
        isPrimaryKey: false;
        isAutoincrement: false;
        hasRuntimeDefault: false;
        enumValues: undefined;
        baseColumn: never;
        identity: undefined;
        generated: undefined;
    }, {}, {}>;
}, undefined, undefined>;
export declare const updateBoardColumnSchema: z.ZodObject<Omit<{
    boardId: z.ZodOptional<z.ZodString>;
    name: z.ZodOptional<z.ZodString>;
    position: z.ZodOptional<z.ZodNumber>;
    color: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    isCollapsed: z.ZodOptional<z.ZodDefault<z.ZodBoolean>>;
    cardLimit: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
}, "id" | "name" | "createdAt" | "updatedAt" | "color" | "boardId" | "position" | "isCollapsed" | "cardLimit">, "strip", z.ZodTypeAny, {}, {}>;
export declare const reorderColumnsSchema: z.ZodObject<{
    columnIds: z.ZodArray<z.ZodString, "many">;
}, "strip", z.ZodTypeAny, {
    columnIds: string[];
}, {
    columnIds: string[];
}>;
export type Board = typeof boards.$inferSelect;
export type NewBoard = typeof boards.$inferInsert;
export type UpdateBoard = z.infer<typeof updateBoardSchema>;
export type BoardColumn = typeof boardColumns.$inferSelect;
export type NewBoardColumn = typeof boardColumns.$inferInsert;
export type UpdateBoardColumn = z.infer<typeof updateBoardColumnSchema>;
//# sourceMappingURL=boards.d.ts.map