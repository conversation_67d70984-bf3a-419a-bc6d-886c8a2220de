"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.insertChecklistItemSchema = exports.insertCardChecklistSchema = exports.updateCardCommentSchema = exports.insertCardCommentSchema = exports.moveCardSchema = exports.updateCardSchema = exports.selectCardSchema = exports.insertCardSchema = exports.checklistItems = exports.cardChecklists = exports.cardComments = exports.cardAssignments = exports.cards = exports.cardPriorityEnum = void 0;
const pg_core_1 = require("drizzle-orm/pg-core");
const drizzle_zod_1 = require("drizzle-zod");
const zod_1 = require("zod");
const boards_1 = require("./boards");
const users_1 = require("./users");
exports.cardPriorityEnum = (0, pg_core_1.pgEnum)('card_priority', ['low', 'medium', 'high', 'urgent']);
exports.cards = (0, pg_core_1.pgTable)('cards', {
    id: (0, pg_core_1.uuid)('id').primaryKey().defaultRandom(),
    boardId: (0, pg_core_1.uuid)('board_id').notNull(),
    columnId: (0, pg_core_1.uuid)('column_id').notNull(),
    title: (0, pg_core_1.varchar)('title', { length: 255 }).notNull(),
    description: (0, pg_core_1.text)('description'),
    position: (0, pg_core_1.integer)('position').notNull(),
    priority: (0, exports.cardPriorityEnum)('priority').default('medium'),
    dueDate: (0, pg_core_1.timestamp)('due_date'),
    startDate: (0, pg_core_1.timestamp)('start_date'),
    estimatedHours: (0, pg_core_1.integer)('estimated_hours'),
    actualHours: (0, pg_core_1.integer)('actual_hours'),
    labels: (0, pg_core_1.jsonb)('labels').$type().default([]),
    cover: (0, pg_core_1.jsonb)('cover').$type(),
    isArchived: (0, pg_core_1.boolean)('is_archived').default(false).notNull(),
    createdBy: (0, pg_core_1.uuid)('created_by').notNull(),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow().notNull(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').defaultNow().notNull(),
}, (table) => ({
    boardFk: (0, pg_core_1.foreignKey)({
        columns: [table.boardId],
        foreignColumns: [boards_1.boards.id],
        name: 'cards_board_id_fk'
    }).onDelete('cascade'),
    columnFk: (0, pg_core_1.foreignKey)({
        columns: [table.columnId],
        foreignColumns: [boards_1.boardColumns.id],
        name: 'cards_column_id_fk'
    }).onDelete('cascade'),
    createdByFk: (0, pg_core_1.foreignKey)({
        columns: [table.createdBy],
        foreignColumns: [users_1.users.id],
        name: 'cards_created_by_fk'
    }).onDelete('restrict'),
}));
exports.cardAssignments = (0, pg_core_1.pgTable)('card_assignments', {
    id: (0, pg_core_1.uuid)('id').primaryKey().defaultRandom(),
    cardId: (0, pg_core_1.uuid)('card_id').notNull(),
    userId: (0, pg_core_1.uuid)('user_id').notNull(),
    assignedBy: (0, pg_core_1.uuid)('assigned_by').notNull(),
    assignedAt: (0, pg_core_1.timestamp)('assigned_at').defaultNow().notNull(),
}, (table) => ({
    cardFk: (0, pg_core_1.foreignKey)({
        columns: [table.cardId],
        foreignColumns: [exports.cards.id],
        name: 'card_assignments_card_id_fk'
    }).onDelete('cascade'),
    userFk: (0, pg_core_1.foreignKey)({
        columns: [table.userId],
        foreignColumns: [users_1.users.id],
        name: 'card_assignments_user_id_fk'
    }).onDelete('cascade'),
    assignedByFk: (0, pg_core_1.foreignKey)({
        columns: [table.assignedBy],
        foreignColumns: [users_1.users.id],
        name: 'card_assignments_assigned_by_fk'
    }).onDelete('restrict'),
}));
exports.cardComments = (0, pg_core_1.pgTable)('card_comments', {
    id: (0, pg_core_1.uuid)('id').primaryKey().defaultRandom(),
    cardId: (0, pg_core_1.uuid)('card_id').notNull(),
    userId: (0, pg_core_1.uuid)('user_id').notNull(),
    content: (0, pg_core_1.text)('content').notNull(),
    isEdited: (0, pg_core_1.boolean)('is_edited').default(false).notNull(),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow().notNull(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').defaultNow().notNull(),
}, (table) => ({
    cardFk: (0, pg_core_1.foreignKey)({
        columns: [table.cardId],
        foreignColumns: [exports.cards.id],
        name: 'card_comments_card_id_fk'
    }).onDelete('cascade'),
    userFk: (0, pg_core_1.foreignKey)({
        columns: [table.userId],
        foreignColumns: [users_1.users.id],
        name: 'card_comments_user_id_fk'
    }).onDelete('cascade'),
}));
exports.cardChecklists = (0, pg_core_1.pgTable)('card_checklists', {
    id: (0, pg_core_1.uuid)('id').primaryKey().defaultRandom(),
    cardId: (0, pg_core_1.uuid)('card_id').notNull(),
    title: (0, pg_core_1.varchar)('title', { length: 255 }).notNull(),
    position: (0, pg_core_1.integer)('position').notNull(),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow().notNull(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').defaultNow().notNull(),
}, (table) => ({
    cardFk: (0, pg_core_1.foreignKey)({
        columns: [table.cardId],
        foreignColumns: [exports.cards.id],
        name: 'card_checklists_card_id_fk'
    }).onDelete('cascade'),
}));
exports.checklistItems = (0, pg_core_1.pgTable)('checklist_items', {
    id: (0, pg_core_1.uuid)('id').primaryKey().defaultRandom(),
    checklistId: (0, pg_core_1.uuid)('checklist_id').notNull(),
    text: (0, pg_core_1.text)('text').notNull(),
    isCompleted: (0, pg_core_1.boolean)('is_completed').default(false).notNull(),
    position: (0, pg_core_1.integer)('position').notNull(),
    completedBy: (0, pg_core_1.uuid)('completed_by'),
    completedAt: (0, pg_core_1.timestamp)('completed_at'),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow().notNull(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').defaultNow().notNull(),
}, (table) => ({
    checklistFk: (0, pg_core_1.foreignKey)({
        columns: [table.checklistId],
        foreignColumns: [exports.cardChecklists.id],
        name: 'checklist_items_checklist_id_fk'
    }).onDelete('cascade'),
    completedByFk: (0, pg_core_1.foreignKey)({
        columns: [table.completedBy],
        foreignColumns: [users_1.users.id],
        name: 'checklist_items_completed_by_fk'
    }).onDelete('set null'),
}));
exports.insertCardSchema = (0, drizzle_zod_1.createInsertSchema)(exports.cards, {
    boardId: zod_1.z.string().uuid('Invalid board ID'),
    columnId: zod_1.z.string().uuid('Invalid column ID'),
    title: zod_1.z.string().min(1, 'Card title is required').max(255),
    description: zod_1.z.string().max(5000).optional(),
    position: zod_1.z.number().int().min(0),
    priority: zod_1.z.enum(['low', 'medium', 'high', 'urgent']).optional(),
    dueDate: zod_1.z.string().datetime().optional(),
    startDate: zod_1.z.string().datetime().optional(),
    estimatedHours: zod_1.z.number().int().min(0).optional(),
    actualHours: zod_1.z.number().int().min(0).optional(),
    labels: zod_1.z.array(zod_1.z.string()).optional(),
    createdBy: zod_1.z.string().uuid('Invalid user ID'),
});
exports.selectCardSchema = (0, drizzle_zod_1.createSelectSchema)(exports.cards);
exports.updateCardSchema = exports.insertCardSchema.partial().omit({
    id: true,
    boardId: true,
    createdBy: true,
    createdAt: true,
    updatedAt: true,
});
exports.moveCardSchema = zod_1.z.object({
    columnId: zod_1.z.string().uuid('Invalid column ID'),
    position: zod_1.z.number().int().min(0),
});
exports.insertCardCommentSchema = (0, drizzle_zod_1.createInsertSchema)(exports.cardComments, {
    cardId: zod_1.z.string().uuid('Invalid card ID'),
    userId: zod_1.z.string().uuid('Invalid user ID'),
    content: zod_1.z.string().min(1, 'Comment content is required').max(2000),
});
exports.updateCardCommentSchema = zod_1.z.object({
    content: zod_1.z.string().min(1, 'Comment content is required').max(2000),
});
exports.insertCardChecklistSchema = (0, drizzle_zod_1.createInsertSchema)(exports.cardChecklists, {
    cardId: zod_1.z.string().uuid('Invalid card ID'),
    title: zod_1.z.string().min(1, 'Checklist title is required').max(255),
    position: zod_1.z.number().int().min(0),
});
exports.insertChecklistItemSchema = (0, drizzle_zod_1.createInsertSchema)(exports.checklistItems, {
    checklistId: zod_1.z.string().uuid('Invalid checklist ID'),
    text: zod_1.z.string().min(1, 'Item text is required').max(500),
    position: zod_1.z.number().int().min(0),
});
//# sourceMappingURL=cards.js.map