import type { LoginData, RegisterData, UserWithoutPassword } from '../db/schema';
export declare class AuthService {
    private readonly saltRounds;
    hashPassword(password: string): Promise<string>;
    comparePassword(password: string, hash: string): Promise<boolean>;
    register(userData: RegisterData): Promise<UserWithoutPassword>;
    login(loginData: LoginData): Promise<UserWithoutPassword>;
    getUserById(userId: string): Promise<UserWithoutPassword | null>;
    updateUser(userId: string, updates: Partial<UserWithoutPassword>): Promise<UserWithoutPassword>;
    changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void>;
    getUserOrganizations(userId: string): Promise<{
        organization: {
            id: string;
            name: string;
            slug: string;
            domain: string | null;
            description: string | null;
            logo: string | null;
            isActive: boolean;
            createdAt: Date;
        };
        membership: {
            role: "viewer" | "member" | "admin" | "owner";
            status: "active" | "inactive" | "pending";
            joinedAt: Date;
        };
    }[]>;
}
export declare const authService: AuthService;
//# sourceMappingURL=authService.d.ts.map