import { Request, Response, NextFunction } from 'express';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                email: string;
                firstName: string;
                lastName: string;
                status: string;
            };
            organization?: {
                id: string;
                name: string;
                slug: string;
            };
            membership?: {
                role: 'viewer' | 'member' | 'admin' | 'owner';
                status: 'active' | 'inactive' | 'pending';
            };
        }
    }
}
export declare const validateSession: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const validateOrganization: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const requirePermission: (permission: string) => (req: Request, res: Response, next: NextFunction) => void;
export declare const requireRole: (minimumRole: "viewer" | "member" | "admin" | "owner") => (req: Request, res: Response, next: NextFunction) => void;
export declare const optionalAuth: (req: Request, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=auth.d.ts.map