"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.invitationService = exports.InvitationService = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const connection_1 = require("../db/connection");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const errorHandler_1 = require("../middleware/errorHandler");
const crypto_1 = require("../utils/crypto");
const validation_1 = require("../utils/validation");
const emailService_1 = require("./emailService");
const authService_1 = require("./authService");
class InvitationService {
    async sendInvitation(organizationId, email, role, invitedBy) {
        try {
            const organization = await connection_1.db
                .select()
                .from(schema_1.organizations)
                .where((0, drizzle_orm_1.eq)(schema_1.organizations.id, organizationId))
                .limit(1);
            if (!organization.length || !organization[0].isActive) {
                throw (0, errorHandler_1.createError)('Organization not found or inactive', 404);
            }
            const org = organization[0];
            if (org.domain && org.settings?.requireInvitation) {
                const isValidDomain = (0, validation_1.isValidEmailDomain)(email, [org.domain]);
                if (!isValidDomain) {
                    throw (0, errorHandler_1.createError)(`Email must be from ${org.domain} domain`, 400);
                }
            }
            const existingUser = await connection_1.db
                .select({ id: schema_1.users.id })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.eq)(schema_1.users.email, email))
                .limit(1);
            if (existingUser.length > 0) {
                const existingMembership = await connection_1.db
                    .select({ id: schema_1.organizationMembers.id })
                    .from(schema_1.organizationMembers)
                    .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.organizationMembers.organizationId, organizationId), (0, drizzle_orm_1.eq)(schema_1.organizationMembers.userId, existingUser[0].id)))
                    .limit(1);
                if (existingMembership.length > 0) {
                    throw (0, errorHandler_1.createError)('User is already a member of this organization', 409);
                }
            }
            const existingInvitation = await connection_1.db
                .select({ id: schema_1.invitations.id })
                .from(schema_1.invitations)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.invitations.organizationId, organizationId), (0, drizzle_orm_1.eq)(schema_1.invitations.email, email), (0, drizzle_orm_1.eq)(schema_1.invitations.status, 'pending')))
                .limit(1);
            if (existingInvitation.length > 0) {
                throw (0, errorHandler_1.createError)('Invitation already sent to this email', 409);
            }
            const token = (0, crypto_1.generateSecureToken)(32);
            const tempPassword = existingUser.length === 0 ? (0, crypto_1.generateRandomPassword)(12) : undefined;
            const expiresAt = new Date();
            expiresAt.setHours(expiresAt.getHours() + parseInt(process.env.INVITATION_TOKEN_EXPIRES_HOURS || '72'));
            const newInvitation = await connection_1.db
                .insert(schema_1.invitations)
                .values({
                organizationId,
                email,
                role,
                token,
                tempPassword,
                invitedBy,
                expiresAt,
                status: 'pending',
            })
                .returning();
            const inviter = await connection_1.db
                .select({
                firstName: schema_1.users.firstName,
                lastName: schema_1.users.lastName,
            })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.eq)(schema_1.users.id, invitedBy))
                .limit(1);
            const inviterName = inviter.length > 0
                ? `${inviter[0].firstName} ${inviter[0].lastName}`
                : 'Team Member';
            const emailTemplate = emailService_1.emailService.generateInvitationEmail(email, org.name, inviterName, role, token, tempPassword);
            await emailService_1.emailService.sendEmail(emailTemplate);
            return {
                invitation: newInvitation[0],
                message: 'Invitation sent successfully',
            };
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('duplicate key')) {
                throw (0, errorHandler_1.createError)('Invitation already exists', 409);
            }
            throw error;
        }
    }
    async getInvitationByToken(token) {
        const invitation = await connection_1.db
            .select({
            invitation: schema_1.invitations,
            organization: {
                id: schema_1.organizations.id,
                name: schema_1.organizations.name,
                logo: schema_1.organizations.logo,
                domain: schema_1.organizations.domain,
            },
        })
            .from(schema_1.invitations)
            .innerJoin(schema_1.organizations, (0, drizzle_orm_1.eq)(schema_1.organizations.id, schema_1.invitations.organizationId))
            .where((0, drizzle_orm_1.eq)(schema_1.invitations.token, token))
            .limit(1);
        if (!invitation.length) {
            throw (0, errorHandler_1.createError)('Invitation not found', 404);
        }
        const inv = invitation[0];
        if (inv.invitation.expiresAt < new Date()) {
            throw (0, errorHandler_1.createError)('Invitation has expired', 410);
        }
        if (inv.invitation.status !== 'pending') {
            throw (0, errorHandler_1.createError)('Invitation is no longer valid', 410);
        }
        return inv;
    }
    async acceptInvitation(token, acceptData) {
        const invitationData = await this.getInvitationByToken(token);
        const invitation = invitationData.invitation;
        const organization = invitationData.organization;
        try {
            let user = await connection_1.db
                .select()
                .from(schema_1.users)
                .where((0, drizzle_orm_1.eq)(schema_1.users.email, invitation.email))
                .limit(1);
            let userId;
            let isNewUser = false;
            if (user.length === 0) {
                const newUser = await authService_1.authService.register({
                    email: invitation.email,
                    password: acceptData.password,
                    confirmPassword: acceptData.confirmPassword,
                    firstName: acceptData.firstName,
                    lastName: acceptData.lastName,
                });
                userId = newUser.id;
                isNewUser = true;
            }
            else {
                userId = user[0].id;
                if (invitation.tempPassword) {
                    const isValidTempPassword = await bcryptjs_1.default.compare(invitation.tempPassword, user[0].passwordHash);
                    if (!isValidTempPassword) {
                        throw (0, errorHandler_1.createError)('Invalid temporary password', 401);
                    }
                }
            }
            await connection_1.db.insert(schema_1.organizationMembers).values({
                organizationId: invitation.organizationId,
                userId,
                role: invitation.role,
                status: 'active',
                invitedBy: invitation.invitedBy,
            });
            await connection_1.db
                .update(schema_1.invitations)
                .set({
                status: 'accepted',
                acceptedBy: userId,
                acceptedAt: new Date(),
                updatedAt: new Date(),
            })
                .where((0, drizzle_orm_1.eq)(schema_1.invitations.id, invitation.id));
            if (isNewUser) {
                const welcomeEmail = emailService_1.emailService.generateWelcomeEmail(invitation.email, acceptData.firstName, organization.name);
                await emailService_1.emailService.sendEmail(welcomeEmail);
            }
            return {
                user: await authService_1.authService.getUserById(userId),
                organization,
                membership: {
                    role: invitation.role,
                    status: 'active',
                },
                isNewUser,
                message: 'Invitation accepted successfully',
            };
        }
        catch (error) {
            await connection_1.db
                .update(schema_1.invitations)
                .set({
                status: 'expired',
                updatedAt: new Date(),
            })
                .where((0, drizzle_orm_1.eq)(schema_1.invitations.id, invitation.id));
            throw error;
        }
    }
    async declineInvitation(token) {
        const invitationData = await this.getInvitationByToken(token);
        const invitation = invitationData.invitation;
        await connection_1.db
            .update(schema_1.invitations)
            .set({
            status: 'declined',
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.invitations.id, invitation.id));
        return {
            message: 'Invitation declined',
        };
    }
    async resendInvitation(invitationId, requestingUserId) {
        const invitation = await connection_1.db
            .select()
            .from(schema_1.invitations)
            .where((0, drizzle_orm_1.eq)(schema_1.invitations.id, invitationId))
            .limit(1);
        if (!invitation.length) {
            throw (0, errorHandler_1.createError)('Invitation not found', 404);
        }
        const inv = invitation[0];
        if (inv.status !== 'pending') {
            throw (0, errorHandler_1.createError)('Can only resend pending invitations', 400);
        }
        const newToken = (0, crypto_1.generateSecureToken)(32);
        const newExpiresAt = new Date();
        newExpiresAt.setHours(newExpiresAt.getHours() + parseInt(process.env.INVITATION_TOKEN_EXPIRES_HOURS || '72'));
        await connection_1.db
            .update(schema_1.invitations)
            .set({
            token: newToken,
            expiresAt: newExpiresAt,
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.invitations.id, invitationId));
        const details = await connection_1.db
            .select({
            organization: {
                name: schema_1.organizations.name,
            },
            inviter: {
                firstName: schema_1.users.firstName,
                lastName: schema_1.users.lastName,
            },
        })
            .from(schema_1.invitations)
            .innerJoin(schema_1.organizations, (0, drizzle_orm_1.eq)(schema_1.organizations.id, schema_1.invitations.organizationId))
            .innerJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.users.id, schema_1.invitations.invitedBy))
            .where((0, drizzle_orm_1.eq)(schema_1.invitations.id, invitationId))
            .limit(1);
        if (details.length > 0) {
            const inviterName = `${details[0].inviter.firstName} ${details[0].inviter.lastName}`;
            const emailTemplate = emailService_1.emailService.generateInvitationEmail(inv.email, details[0].organization.name, inviterName, inv.role, newToken, inv.tempPassword);
            await emailService_1.emailService.sendEmail(emailTemplate);
        }
        return {
            message: 'Invitation resent successfully',
        };
    }
    async cleanupExpiredInvitations() {
        const result = await connection_1.db
            .update(schema_1.invitations)
            .set({
            status: 'expired',
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.invitations.status, 'pending'), (0, drizzle_orm_1.lt)(schema_1.invitations.expiresAt, new Date())))
            .returning({ id: schema_1.invitations.id });
        return {
            expiredCount: result.length,
            message: `Marked ${result.length} invitations as expired`,
        };
    }
    async getOrganizationInvitations(organizationId) {
        const invitationList = await connection_1.db
            .select({
            invitation: schema_1.invitations,
            inviter: {
                firstName: schema_1.users.firstName,
                lastName: schema_1.users.lastName,
                email: schema_1.users.email,
            },
        })
            .from(schema_1.invitations)
            .innerJoin(schema_1.users, (0, drizzle_orm_1.eq)(schema_1.users.id, schema_1.invitations.invitedBy))
            .where((0, drizzle_orm_1.eq)(schema_1.invitations.organizationId, organizationId))
            .orderBy(schema_1.invitations.createdAt);
        return invitationList;
    }
}
exports.InvitationService = InvitationService;
exports.invitationService = new InvitationService();
//# sourceMappingURL=invitationService.js.map