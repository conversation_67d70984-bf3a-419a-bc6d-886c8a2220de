"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.requireRole = exports.requirePermission = exports.validateOrganization = exports.validateSession = void 0;
const connection_1 = require("../db/connection");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const errorHandler_1 = require("./errorHandler");
const validateSession = async (req, res, next) => {
    try {
        if (!req.session || !req.session.userId) {
            throw (0, errorHandler_1.createError)('Authentication required', 401);
        }
        const user = await connection_1.db
            .select({
            id: schema_1.users.id,
            email: schema_1.users.email,
            firstName: schema_1.users.firstName,
            lastName: schema_1.users.lastName,
            status: schema_1.users.status,
        })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, req.session.userId))
            .limit(1);
        if (!user.length || user[0].status !== 'active') {
            req.session.destroy((err) => {
                if (err)
                    console.error('Session destruction error:', err);
            });
            throw (0, errorHandler_1.createError)('Invalid or inactive user session', 401);
        }
        req.user = user[0];
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.validateSession = validateSession;
const validateOrganization = async (req, res, next) => {
    try {
        if (!req.user) {
            throw (0, errorHandler_1.createError)('User authentication required', 401);
        }
        const organizationId = req.params.organizationId || req.body.organizationId || req.query.organizationId;
        if (!organizationId) {
            throw (0, errorHandler_1.createError)('Organization ID is required', 400);
        }
        const membership = await connection_1.db
            .select({
            organization: {
                id: schema_1.organizations.id,
                name: schema_1.organizations.name,
                slug: schema_1.organizations.slug,
            },
            role: schema_1.organizationMembers.role,
            status: schema_1.organizationMembers.status,
        })
            .from(schema_1.organizationMembers)
            .innerJoin(schema_1.organizations, (0, drizzle_orm_1.eq)(schema_1.organizations.id, schema_1.organizationMembers.organizationId))
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.organizationMembers.organizationId, organizationId), (0, drizzle_orm_1.eq)(schema_1.organizationMembers.userId, req.user.id), (0, drizzle_orm_1.eq)(schema_1.organizationMembers.status, 'active')))
            .limit(1);
        if (!membership.length) {
            throw (0, errorHandler_1.createError)('Access denied: Not a member of this organization', 403);
        }
        req.organization = membership[0].organization;
        req.membership = {
            role: membership[0].role,
            status: membership[0].status,
        };
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.validateOrganization = validateOrganization;
const requirePermission = (permission) => {
    return (req, res, next) => {
        try {
            if (!req.membership) {
                throw (0, errorHandler_1.createError)('Organization membership required', 403);
            }
            const userRole = req.membership.role;
            const { PERMISSIONS } = require('../db/schema/organizationMembers');
            if (!PERMISSIONS[permission] || !PERMISSIONS[permission].includes(userRole)) {
                throw (0, errorHandler_1.createError)(`Insufficient permissions: ${permission} required`, 403);
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.requirePermission = requirePermission;
const requireRole = (minimumRole) => {
    return (req, res, next) => {
        try {
            if (!req.membership) {
                throw (0, errorHandler_1.createError)('Organization membership required', 403);
            }
            const { ROLE_HIERARCHY } = require('../db/schema/organizationMembers');
            const userRoleLevel = ROLE_HIERARCHY[req.membership.role];
            const requiredRoleLevel = ROLE_HIERARCHY[minimumRole];
            if (userRoleLevel < requiredRoleLevel) {
                throw (0, errorHandler_1.createError)(`Insufficient role: ${minimumRole} or higher required`, 403);
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.requireRole = requireRole;
const optionalAuth = async (req, res, next) => {
    try {
        if (req.session && req.session.userId) {
            const user = await connection_1.db
                .select({
                id: schema_1.users.id,
                email: schema_1.users.email,
                firstName: schema_1.users.firstName,
                lastName: schema_1.users.lastName,
                status: schema_1.users.status,
            })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.eq)(schema_1.users.id, req.session.userId))
                .limit(1);
            if (user.length && user[0].status === 'active') {
                req.user = user[0];
            }
        }
        next();
    }
    catch (error) {
        next();
    }
};
exports.optionalAuth = optionalAuth;
//# sourceMappingURL=auth.js.map