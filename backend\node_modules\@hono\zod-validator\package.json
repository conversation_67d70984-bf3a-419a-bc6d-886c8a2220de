{"name": "@hono/zod-validator", "version": "0.2.2", "description": "Validator middleware using Zod", "type": "module", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {".": {"types": "./dist/esm/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "files": ["dist"], "scripts": {"test": "vitest --run", "copy:package.cjs.json": "cp ./package.cjs.json ./dist/cjs/package.json", "build:cjs": "tsc -p tsconfig.cjs.json", "build:esm": "tsc -p tsconfig.esm.json", "build": "rimraf dist && yarn build:cjs && yarn build:esm && yarn copy:package.cjs.json", "prerelease": "yarn build && yarn test", "release": "yarn publish"}, "license": "MIT", "publishConfig": {"registry": "https://registry.npmjs.org", "access": "public"}, "repository": {"type": "git", "url": "https://github.com/honojs/middleware.git"}, "homepage": "https://github.com/honojs/middleware", "peerDependencies": {"hono": ">=3.9.0", "zod": "^3.19.1"}, "devDependencies": {"hono": "^4.0.10", "jest": "^29.7.0", "rimraf": "^5.0.5", "typescript": "^5.3.3", "vitest": "^1.4.0", "zod": "^3.22.4"}}