import type { <PERSON><PERSON><PERSON>, Update<PERSON>ard, Card, NewCardComment, NewCardChecklist, NewChecklistItem, CardComment, CardChecklist, ChecklistItem } from '../db/schema';
export declare class CardService {
    createCard(cardData: Omit<NewCard, 'id' | 'createdAt' | 'updatedAt'>, createdBy: string): Promise<Card>;
    getCardById(cardId: string, includeDetails?: boolean): Promise<{
        id: string;
        boardId: string;
        columnId: string;
        title: string;
        description: string | null;
        position: number;
        priority: "low" | "medium" | "high" | "urgent" | null;
        dueDate: Date | null;
        startDate: Date | null;
        estimatedHours: number | null;
        actualHours: number | null;
        labels: string[] | null;
        cover: {
            type: "color" | "image";
            value: string;
        } | null;
        isArchived: boolean;
        createdBy: string;
        createdAt: Date;
        updatedAt: Date;
    } | {
        assignments: {
            assignment: {
                id: string;
                cardId: string;
                userId: string;
                assignedBy: string;
                assignedAt: Date;
            };
            user: {
                id: string;
                email: string;
                firstName: string;
                lastName: string;
                avatar: string | null;
            };
        }[];
        comments: {
            comment: {
                id: string;
                cardId: string;
                userId: string;
                content: string;
                isEdited: boolean;
                createdAt: Date;
                updatedAt: Date;
            };
            user: {
                id: string;
                firstName: string;
                lastName: string;
                avatar: string | null;
            };
        }[];
        checklists: {
            items: {
                id: string;
                checklistId: string;
                text: string;
                isCompleted: boolean;
                position: number;
                completedBy: string | null;
                completedAt: Date | null;
                createdAt: Date;
                updatedAt: Date;
            }[];
            id: string;
            cardId: string;
            title: string;
            position: number;
            createdAt: Date;
            updatedAt: Date;
        }[];
        id?: string;
        boardId?: string;
        columnId?: string;
        title?: string;
        description?: string | null;
        position?: number;
        priority?: "low" | "medium" | "high" | "urgent" | null;
        dueDate?: Date | null;
        startDate?: Date | null;
        estimatedHours?: number | null;
        actualHours?: number | null;
        labels?: string[] | null;
        cover?: {
            type: "color" | "image";
            value: string;
        } | null;
        isArchived?: boolean;
        createdBy?: string;
        createdAt?: Date;
        updatedAt?: Date;
    } | undefined>;
    getBoardCards(boardId: string, includeArchived?: boolean): Promise<{
        column: any;
        cards: Card[];
    }[]>;
    updateCard(cardId: string, updates: UpdateCard, userId: string): Promise<Card>;
    moveCard(cardId: string, columnId: string, position: number, userId: string): Promise<Card>;
    deleteCard(cardId: string, userId: string): Promise<void>;
    assignUserToCard(cardId: string, userId: string, assignedBy: string): Promise<{
        id: string;
        userId: string;
        cardId: string;
        assignedBy: string;
        assignedAt: Date;
    } | undefined>;
    removeUserFromCard(cardId: string, userId: string): Promise<void>;
    getCardAssignments(cardId: string): Promise<{
        assignment: {
            id: string;
            cardId: string;
            userId: string;
            assignedBy: string;
            assignedAt: Date;
        };
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            avatar: string | null;
        };
    }[]>;
    addCardComment(commentData: Omit<NewCardComment, 'id' | 'createdAt' | 'updatedAt'>): Promise<CardComment>;
    updateCardComment(commentId: string, content: string, userId: string): Promise<CardComment>;
    deleteCardComment(commentId: string, userId: string): Promise<void>;
    getCardComments(cardId: string): Promise<{
        comment: {
            id: string;
            cardId: string;
            userId: string;
            content: string;
            isEdited: boolean;
            createdAt: Date;
            updatedAt: Date;
        };
        user: {
            id: string;
            firstName: string;
            lastName: string;
            avatar: string | null;
        };
    }[]>;
    createCardChecklist(checklistData: Omit<NewCardChecklist, 'id' | 'createdAt' | 'updatedAt'>): Promise<CardChecklist>;
    getCardChecklists(cardId: string): Promise<{
        items: {
            id: string;
            checklistId: string;
            text: string;
            isCompleted: boolean;
            position: number;
            completedBy: string | null;
            completedAt: Date | null;
            createdAt: Date;
            updatedAt: Date;
        }[];
        id: string;
        cardId: string;
        title: string;
        position: number;
        createdAt: Date;
        updatedAt: Date;
    }[]>;
    addChecklistItem(itemData: Omit<NewChecklistItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<ChecklistItem>;
    toggleChecklistItem(itemId: string, userId: string): Promise<ChecklistItem>;
}
export declare const cardService: CardService;
//# sourceMappingURL=cardService.d.ts.map