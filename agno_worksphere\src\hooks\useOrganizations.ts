import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiService } from '../services/api';
import type { Organization, OrganizationMembership } from '../types/api';

// Query keys
export const organizationKeys = {
  all: ['organizations'] as const,
  myOrganizations: (query?: string) => ['organizations', 'my', { query }] as const,
  detail: (id: string) => ['organizations', 'detail', id] as const,
  members: (id: string) => ['organizations', 'members', id] as const,
};

// Organization queries
export const useMyOrganizations = (query?: string) => {
  return useQuery({
    queryKey: organizationKeys.myOrganizations(query),
    queryFn: async () => {
      const response = await apiService.organizations.getMyOrganizations(query);
      return response.data as { organizations: OrganizationMembership[] };
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useOrganization = (organizationId: string) => {
  return useQuery({
    queryKey: organizationKeys.detail(organizationId),
    queryFn: async () => {
      const response = await apiService.organizations.getById(organizationId);
      return response.data as {
        organization: Organization;
        membership: { role: string; status: string };
        stats: any;
      };
    },
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useOrganizationMembers = (organizationId: string) => {
  return useQuery({
    queryKey: organizationKeys.members(organizationId),
    queryFn: async () => {
      const response = await apiService.organizations.getMembers(organizationId);
      return response.data as { members: any[] };
    },
    enabled: !!organizationId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Organization mutations
export const useCreateOrganization = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: any) => {
      const response = await apiService.organizations.create(data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: organizationKeys.all });
    },
  });
};

export const useUpdateOrganization = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ organizationId, data }: { organizationId: string; data: any }) => {
      const response = await apiService.organizations.update(organizationId, data);
      return response;
    },
    onSuccess: (_, { organizationId }) => {
      queryClient.invalidateQueries({ queryKey: organizationKeys.detail(organizationId) });
      queryClient.invalidateQueries({ queryKey: organizationKeys.all });
    },
  });
};

export const useDeleteOrganization = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (organizationId: string) => {
      const response = await apiService.organizations.delete(organizationId);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: organizationKeys.all });
    },
  });
};




export const useSwitchOrganization = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (organizationId: string) => {
      const response = await apiService.organizations.switch(organizationId);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate all cached data since context changed
      queryClient.invalidateQueries();
    },
  });
};

export const useUpdateMemberRole = (organizationId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ memberId, role }: { memberId: string; role: string }) => {
      const response = await apiService.organizations.updateMemberRole(organizationId, memberId, { role });
      return response.data;
    },
    onSuccess: () => {
      // Invalidate members list
      queryClient.invalidateQueries({ queryKey: organizationKeys.members(organizationId) });
    },
  });
};

export const useRemoveMember = (organizationId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (memberId: string) => {
      const response = await apiService.organizations.removeMember(organizationId, memberId);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate members list
      queryClient.invalidateQueries({ queryKey: organizationKeys.members(organizationId) });
    },
  });
};

// Invitation hooks
export const useSendInvitation = (organizationId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { email: string; role: string }) => {
      const response = await apiService.invitations.send(organizationId, data);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate members list and invitations
      queryClient.invalidateQueries({ queryKey: organizationKeys.members(organizationId) });
      queryClient.invalidateQueries({ queryKey: ['invitations', organizationId] });
    },
  });
};

export const useOrganizationInvitations = (organizationId: string) => {
  return useQuery({
    queryKey: ['invitations', organizationId],
    queryFn: async () => {
      const response = await apiService.invitations.getOrganizationInvitations(organizationId);
      return response.data as { invitations: any[] };
    },
    enabled: !!organizationId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useCancelInvitation = (organizationId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (invitationId: string) => {
      const response = await apiService.invitations.cancel(invitationId);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate invitations list
      queryClient.invalidateQueries({ queryKey: ['invitations', organizationId] });
    },
  });
};

// Organization statistics
export const useOrganizationStats = (organizationId: string) => {
  return useQuery({
    queryKey: ['organizations', 'stats', organizationId],
    queryFn: async () => {
      const response = await apiService.organizations.getStats(organizationId);
      return response.data as {
        members: {
          total: number;
          byRole: Record<string, number>;
          pending: number;
        };
        boards: { total: number };
        cards: { total: number };
      };
    },
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Bulk member operations
export const useBulkUpdateMemberRoles = (organizationId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { memberIds: string[]; role: string }) => {
      const response = await apiService.organizations.bulkUpdateMemberRoles(organizationId, data);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate members list
      queryClient.invalidateQueries({ queryKey: organizationKeys.members(organizationId) });
      queryClient.invalidateQueries({ queryKey: ['organizations', 'stats', organizationId] });
    },
  });
};
