"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authService = exports.AuthService = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const connection_1 = require("../db/connection");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const errorHandler_1 = require("../middleware/errorHandler");
class AuthService {
    saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
    async hashPassword(password) {
        return bcryptjs_1.default.hash(password, this.saltRounds);
    }
    async comparePassword(password, hash) {
        return bcryptjs_1.default.compare(password, hash);
    }
    async register(userData) {
        try {
            const existingUser = await connection_1.db
                .select({ id: schema_1.users.id })
                .from(schema_1.users)
                .where((0, drizzle_orm_1.eq)(schema_1.users.email, userData.email))
                .limit(1);
            if (existingUser.length > 0) {
                throw (0, errorHandler_1.createError)('User with this email already exists', 409);
            }
            const passwordHash = await this.hashPassword(userData.password);
            const newUser = await connection_1.db
                .insert(schema_1.users)
                .values({
                email: userData.email,
                passwordHash,
                firstName: userData.firstName,
                lastName: userData.lastName,
                avatar: userData.avatar,
                bio: userData.bio,
                phone: userData.phone,
                timezone: userData.timezone || 'UTC',
                language: userData.language || 'en',
                status: 'active',
                isEmailVerified: false,
            })
                .returning({
                id: schema_1.users.id,
                email: schema_1.users.email,
                firstName: schema_1.users.firstName,
                lastName: schema_1.users.lastName,
                avatar: schema_1.users.avatar,
                bio: schema_1.users.bio,
                phone: schema_1.users.phone,
                timezone: schema_1.users.timezone,
                language: schema_1.users.language,
                status: schema_1.users.status,
                isEmailVerified: schema_1.users.isEmailVerified,
                createdAt: schema_1.users.createdAt,
                updatedAt: schema_1.users.updatedAt,
            });
            return newUser[0];
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('duplicate key')) {
                throw (0, errorHandler_1.createError)('User with this email already exists', 409);
            }
            throw error;
        }
    }
    async login(loginData) {
        const user = await connection_1.db
            .select()
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.email, loginData.email))
            .limit(1);
        if (!user.length) {
            throw (0, errorHandler_1.createError)('Invalid email or password', 401);
        }
        const userData = user[0];
        if (userData.status !== 'active') {
            throw (0, errorHandler_1.createError)('Account is not active', 401);
        }
        const isPasswordValid = await this.comparePassword(loginData.password, userData.passwordHash);
        if (!isPasswordValid) {
            throw (0, errorHandler_1.createError)('Invalid email or password', 401);
        }
        await connection_1.db
            .update(schema_1.users)
            .set({ lastLoginAt: new Date() })
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, userData.id));
        const { passwordHash, ...userWithoutPassword } = userData;
        return userWithoutPassword;
    }
    async getUserById(userId) {
        const user = await connection_1.db
            .select({
            id: schema_1.users.id,
            email: schema_1.users.email,
            firstName: schema_1.users.firstName,
            lastName: schema_1.users.lastName,
            avatar: schema_1.users.avatar,
            bio: schema_1.users.bio,
            phone: schema_1.users.phone,
            timezone: schema_1.users.timezone,
            language: schema_1.users.language,
            status: schema_1.users.status,
            isEmailVerified: schema_1.users.isEmailVerified,
            lastLoginAt: schema_1.users.lastLoginAt,
            createdAt: schema_1.users.createdAt,
            updatedAt: schema_1.users.updatedAt,
        })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, userId))
            .limit(1);
        return user.length > 0 ? user[0] : null;
    }
    async updateUser(userId, updates) {
        const updatedUser = await connection_1.db
            .update(schema_1.users)
            .set({
            ...updates,
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, userId))
            .returning({
            id: schema_1.users.id,
            email: schema_1.users.email,
            firstName: schema_1.users.firstName,
            lastName: schema_1.users.lastName,
            avatar: schema_1.users.avatar,
            bio: schema_1.users.bio,
            phone: schema_1.users.phone,
            timezone: schema_1.users.timezone,
            language: schema_1.users.language,
            status: schema_1.users.status,
            isEmailVerified: schema_1.users.isEmailVerified,
            lastLoginAt: schema_1.users.lastLoginAt,
            createdAt: schema_1.users.createdAt,
            updatedAt: schema_1.users.updatedAt,
        });
        if (!updatedUser.length) {
            throw (0, errorHandler_1.createError)('User not found', 404);
        }
        return updatedUser[0];
    }
    async changePassword(userId, currentPassword, newPassword) {
        const user = await connection_1.db
            .select({ passwordHash: schema_1.users.passwordHash })
            .from(schema_1.users)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, userId))
            .limit(1);
        if (!user.length) {
            throw (0, errorHandler_1.createError)('User not found', 404);
        }
        const isCurrentPasswordValid = await this.comparePassword(currentPassword, user[0].passwordHash);
        if (!isCurrentPasswordValid) {
            throw (0, errorHandler_1.createError)('Current password is incorrect', 400);
        }
        const newPasswordHash = await this.hashPassword(newPassword);
        await connection_1.db
            .update(schema_1.users)
            .set({
            passwordHash: newPasswordHash,
            passwordChangedAt: new Date(),
            updatedAt: new Date(),
        })
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, userId));
    }
    async getUserOrganizations(userId) {
        const userOrgs = await connection_1.db
            .select({
            organization: {
                id: schema_1.organizations.id,
                name: schema_1.organizations.name,
                slug: schema_1.organizations.slug,
                domain: schema_1.organizations.domain,
                description: schema_1.organizations.description,
                logo: schema_1.organizations.logo,
                isActive: schema_1.organizations.isActive,
                createdAt: schema_1.organizations.createdAt,
            },
            membership: {
                role: schema_1.organizationMembers.role,
                status: schema_1.organizationMembers.status,
                joinedAt: schema_1.organizationMembers.joinedAt,
            },
        })
            .from(schema_1.organizationMembers)
            .innerJoin(schema_1.organizations, (0, drizzle_orm_1.eq)(schema_1.organizations.id, schema_1.organizationMembers.organizationId))
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.organizationMembers.userId, userId), (0, drizzle_orm_1.eq)(schema_1.organizationMembers.status, 'active'), (0, drizzle_orm_1.eq)(schema_1.organizations.isActive, true)))
            .orderBy(schema_1.organizationMembers.joinedAt);
        return userOrgs;
    }
}
exports.AuthService = AuthService;
exports.authService = new AuthService();
//# sourceMappingURL=authService.js.map