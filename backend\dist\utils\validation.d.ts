import { z } from 'zod';
export declare const uuidSchema: z.ZodString;
export declare const emailSchema: z.ZodString;
export declare const passwordSchema: z.ZodString;
export declare const paginationSchema: z.ZodObject<{
    page: z.<PERSON>od<PERSON>efault<z.ZodNumber>;
    limit: z.<PERSON><PERSON><PERSON><z.ZodNumber>;
    sortBy: z.Zod<PERSON>ptional<z.ZodString>;
    sortOrder: z.Z<PERSON><PERSON><PERSON><z.ZodEnum<["asc", "desc"]>>;
}, "strip", z.ZodTypeAny, {
    limit: number;
    page: number;
    sortOrder: "asc" | "desc";
    sortBy?: string | undefined;
}, {
    limit?: number | undefined;
    page?: number | undefined;
    sortBy?: string | undefined;
    sortOrder?: "asc" | "desc" | undefined;
}>;
export declare const searchSchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    limit: z.<PERSON>efault<z.ZodN<PERSON>ber>;
    sortBy: z.<PERSON>ptional<z.ZodString>;
    sortOrder: z.<PERSON><PERSON><PERSON><z.ZodEnum<["asc", "desc"]>>;
    q: z.ZodString;
}, "strip", z.ZodTypeAny, {
    limit: number;
    page: number;
    sortOrder: "asc" | "desc";
    q: string;
    sortBy?: string | undefined;
}, {
    q: string;
    limit?: number | undefined;
    page?: number | undefined;
    sortBy?: string | undefined;
    sortOrder?: "asc" | "desc" | undefined;
}>;
export declare const isValidUUID: (value: string) => boolean;
export declare const sanitizeSearchQuery: (query: string) => string;
export declare const isValidEmailDomain: (email: string, allowedDomains?: string[]) => boolean;
//# sourceMappingURL=validation.d.ts.map