"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.reorderColumnsSchema = exports.updateBoardColumnSchema = exports.selectBoardColumnSchema = exports.insertBoardColumnSchema = exports.updateBoardSchema = exports.selectBoardSchema = exports.insertBoardSchema = exports.boardColumns = exports.boards = void 0;
const pg_core_1 = require("drizzle-orm/pg-core");
const drizzle_zod_1 = require("drizzle-zod");
const zod_1 = require("zod");
const organizations_1 = require("./organizations");
const users_1 = require("./users");
exports.boards = (0, pg_core_1.pgTable)('boards', {
    id: (0, pg_core_1.uuid)('id').primaryKey().defaultRandom(),
    organizationId: (0, pg_core_1.uuid)('organization_id').notNull(),
    name: (0, pg_core_1.varchar)('name', { length: 255 }).notNull(),
    description: (0, pg_core_1.text)('description'),
    color: (0, pg_core_1.varchar)('color', { length: 7 }).default('#3B82F6'),
    isTemplate: (0, pg_core_1.boolean)('is_template').default(false).notNull(),
    isArchived: (0, pg_core_1.boolean)('is_archived').default(false).notNull(),
    settings: (0, pg_core_1.jsonb)('settings').$type().default({}),
    createdBy: (0, pg_core_1.uuid)('created_by').notNull(),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow().notNull(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').defaultNow().notNull(),
}, (table) => ({
    organizationFk: (0, pg_core_1.foreignKey)({
        columns: [table.organizationId],
        foreignColumns: [organizations_1.organizations.id],
        name: 'boards_organization_id_fk'
    }).onDelete('cascade'),
    createdByFk: (0, pg_core_1.foreignKey)({
        columns: [table.createdBy],
        foreignColumns: [users_1.users.id],
        name: 'boards_created_by_fk'
    }).onDelete('restrict'),
}));
exports.boardColumns = (0, pg_core_1.pgTable)('board_columns', {
    id: (0, pg_core_1.uuid)('id').primaryKey().defaultRandom(),
    boardId: (0, pg_core_1.uuid)('board_id').notNull(),
    name: (0, pg_core_1.varchar)('name', { length: 255 }).notNull(),
    position: (0, pg_core_1.integer)('position').notNull(),
    color: (0, pg_core_1.varchar)('color', { length: 7 }).default('#6B7280'),
    isCollapsed: (0, pg_core_1.boolean)('is_collapsed').default(false).notNull(),
    cardLimit: (0, pg_core_1.integer)('card_limit'),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow().notNull(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').defaultNow().notNull(),
}, (table) => ({
    boardFk: (0, pg_core_1.foreignKey)({
        columns: [table.boardId],
        foreignColumns: [exports.boards.id],
        name: 'board_columns_board_id_fk'
    }).onDelete('cascade'),
}));
exports.insertBoardSchema = zod_1.z.object({
    organizationId: zod_1.z.string().uuid('Invalid organization ID'),
    name: zod_1.z.string().min(1, 'Board name is required').max(255),
    description: zod_1.z.string().max(1000).optional(),
    color: zod_1.z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color').optional(),
    isTemplate: zod_1.z.boolean().default(false),
    isArchived: zod_1.z.boolean().default(false),
    settings: zod_1.z.record(zod_1.z.any()).optional(),
    createdBy: zod_1.z.string().uuid('Invalid user ID'),
});
exports.selectBoardSchema = zod_1.z.object({
    id: zod_1.z.string(),
    organizationId: zod_1.z.string(),
    name: zod_1.z.string(),
    description: zod_1.z.string().nullable(),
    color: zod_1.z.string().nullable(),
    isTemplate: zod_1.z.boolean(),
    isArchived: zod_1.z.boolean(),
    settings: zod_1.z.record(zod_1.z.any()).nullable(),
    createdBy: zod_1.z.string(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date(),
});
exports.updateBoardSchema = exports.insertBoardSchema.partial().omit({
    organizationId: true,
    createdBy: true,
});
exports.insertBoardColumnSchema = zod_1.z.object({
    boardId: zod_1.z.string().uuid('Invalid board ID'),
    name: zod_1.z.string().min(1, 'Column name is required').max(255),
    position: zod_1.z.number().int().min(0),
    color: zod_1.z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color').optional(),
    isCollapsed: zod_1.z.boolean().default(false),
    cardLimit: zod_1.z.number().int().min(1).optional(),
});
exports.selectBoardColumnSchema = (0, drizzle_zod_1.createSelectSchema)(exports.boardColumns);
exports.updateBoardColumnSchema = exports.insertBoardColumnSchema.partial().omit({
    id: true,
    boardId: true,
    createdAt: true,
    updatedAt: true,
});
exports.reorderColumnsSchema = zod_1.z.object({
    columnIds: zod_1.z.array(zod_1.z.string().uuid('Invalid column ID')),
});
//# sourceMappingURL=boards.js.map