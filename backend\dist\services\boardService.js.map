{"version": 3, "file": "boardService.js", "sourceRoot": "", "sources": ["../../src/services/boardService.ts"], "names": [], "mappings": ";;;AAAA,iDAAsC;AACtC,yCAA0E;AAC1E,6CAAiD;AACjD,6DAAyD;AAGzD,MAAa,YAAY;IAIvB,KAAK,CAAC,WAAW,CAAC,SAA2D,EAAE,SAAiB;QAC9F,IAAI,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,eAAE;iBAC1B,MAAM,CAAC,EAAE,EAAE,EAAE,sBAAa,CAAC,EAAE,EAAE,QAAQ,EAAE,sBAAa,CAAC,QAAQ,EAAE,CAAC;iBAClE,IAAI,CAAC,sBAAa,CAAC;iBACnB,KAAK,CAAC,IAAA,gBAAE,EAAC,sBAAa,CAAC,EAAE,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC;iBACrD,KAAK,CAAC,CAAC,CAAC,CAAC;YAEZ,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACtD,MAAM,IAAA,0BAAW,EAAC,oCAAoC,EAAE,GAAG,CAAC,CAAC;YAC/D,CAAC;YAGD,MAAM,QAAQ,GAAG,MAAM,eAAE;iBACtB,MAAM,CAAC,eAAM,CAAC;iBACd,MAAM,CAAC;gBACN,GAAG,SAAS;gBACZ,SAAS;gBACT,QAAQ,EAAE,SAAS,CAAC,QAAQ,IAAI;oBAC9B,aAAa,EAAE,IAAI;oBACnB,gBAAgB,EAAE,IAAI;oBACtB,gBAAgB,EAAE,IAAI;oBACtB,gBAAgB,EAAE,IAAI;oBACtB,kBAAkB,EAAE,IAAI;oBACxB,UAAU,EAAE,cAAc;iBAC3B;aACF,CAAC;iBACD,SAAS,EAAE,CAAC;YAEf,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAG1B,MAAM,cAAc,GAAG;gBACrB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;gBAChD,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;gBACtD,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;aAChD,CAAC;YAEF,MAAM,eAAE,CAAC,MAAM,CAAC,qBAAY,CAAC,CAAC,MAAM,CAClC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACzB,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,QAAQ,EAAE,GAAG,CAAC,QAAQ;gBACtB,KAAK,EAAE,GAAG,CAAC,KAAK;aACjB,CAAC,CAAC,CACJ,CAAC;YAEF,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;gBACpE,MAAM,IAAA,0BAAW,EAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC;YAC3D,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,OAAe,EAAE,iBAA0B,IAAI;QAChE,MAAM,KAAK,GAAG,MAAM,eAAE;aACnB,MAAM,EAAE;aACR,IAAI,CAAC,eAAM,CAAC;aACZ,KAAK,CAAC,IAAA,gBAAE,EAAC,eAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;aAC7B,KAAK,CAAC,CAAC,CAAC,CAAC;QAEZ,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAClB,MAAM,IAAA,0BAAW,EAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAE3B,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpD,OAAO;gBACL,GAAG,SAAS;gBACZ,OAAO;aACR,CAAC;QACJ,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,cAAsB,EAAE,kBAA2B,KAAK;QAClF,IAAI,cAAc,GAAG,IAAA,gBAAE,EAAC,eAAM,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;QAE/D,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,cAAc,GAAG,IAAA,iBAAG,EAAC,cAAc,EAAE,IAAA,gBAAE,EAAC,eAAM,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;QACrE,CAAC;QAED,MAAM,kBAAkB,GAAG,MAAM,eAAE;aAChC,MAAM,EAAE;aACR,IAAI,CAAC,eAAM,CAAC;aACZ,KAAK,CAAC,cAAc,CAAC;aACrB,OAAO,CAAC,IAAA,kBAAI,EAAC,eAAM,CAAC,SAAS,CAAC,CAAC,CAAC;QAEnC,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,OAAoB,EAAE,MAAc;QAErE,MAAM,aAAa,GAAG,MAAM,eAAE;aAC3B,MAAM,EAAE;aACR,IAAI,CAAC,eAAM,CAAC;aACZ,KAAK,CAAC,IAAA,gBAAE,EAAC,eAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;aAC7B,KAAK,CAAC,CAAC,CAAC,CAAC;QAEZ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC1B,MAAM,IAAA,0BAAW,EAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QAC5C,CAAC;QAGD,MAAM,YAAY,GAAG,MAAM,eAAE;aAC1B,MAAM,CAAC,eAAM,CAAC;aACd,GAAG,CAAC;YACH,GAAG,OAAO;YACV,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;aACD,KAAK,CAAC,IAAA,gBAAE,EAAC,eAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;aAC7B,SAAS,EAAE,CAAC;QAEf,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YACzB,MAAM,IAAA,0BAAW,EAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,MAAc;QAC/C,MAAM,MAAM,GAAG,MAAM,eAAE;aACpB,MAAM,CAAC,eAAM,CAAC;aACd,GAAG,CAAC;YACH,UAAU,EAAE,IAAI;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;aACD,KAAK,CAAC,IAAA,gBAAE,EAAC,eAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;aAC7B,SAAS,CAAC,EAAE,EAAE,EAAE,eAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,IAAA,0BAAW,EAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,OAAe;QACnC,MAAM,OAAO,GAAG,MAAM,eAAE;aACrB,MAAM,EAAE;aACR,IAAI,CAAC,qBAAY,CAAC;aAClB,KAAK,CAAC,IAAA,gBAAE,EAAC,qBAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aACxC,OAAO,CAAC,IAAA,iBAAG,EAAC,qBAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEvC,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,UAAkE;QACxF,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG,MAAM,eAAE;iBACnB,MAAM,CAAC,EAAE,EAAE,EAAE,eAAM,CAAC,EAAE,EAAE,CAAC;iBACzB,IAAI,CAAC,eAAM,CAAC;iBACZ,KAAK,CAAC,IAAA,gBAAE,EAAC,eAAM,CAAC,EAAE,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;iBACxC,KAAK,CAAC,CAAC,CAAC,CAAC;YAEZ,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBAClB,MAAM,IAAA,0BAAW,EAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;YAC5C,CAAC;YAGD,IAAI,UAAU,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACtC,MAAM,UAAU,GAAG,MAAM,eAAE;qBACxB,MAAM,CAAC,EAAE,QAAQ,EAAE,qBAAY,CAAC,QAAQ,EAAE,CAAC;qBAC3C,IAAI,CAAC,qBAAY,CAAC;qBAClB,KAAK,CAAC,IAAA,gBAAE,EAAC,qBAAY,CAAC,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;qBACnD,OAAO,CAAC,IAAA,kBAAI,EAAC,qBAAY,CAAC,QAAQ,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEZ,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/E,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,eAAE;iBACvB,MAAM,CAAC,qBAAY,CAAC;iBACpB,MAAM,CAAC,UAAU,CAAC;iBAClB,SAAS,EAAE,CAAC;YAEf,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;gBACpE,MAAM,IAAA,0BAAW,EAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;YACpD,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,OAA0B;QAClE,MAAM,aAAa,GAAG,MAAM,eAAE;aAC3B,MAAM,CAAC,qBAAY,CAAC;aACpB,GAAG,CAAC;YACH,GAAG,OAAO;YACV,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;aACD,KAAK,CAAC,IAAA,gBAAE,EAAC,qBAAY,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;aACpC,SAAS,EAAE,CAAC;QAEf,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC1B,MAAM,IAAA,0BAAW,EAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QAEtC,MAAM,aAAa,GAAG,MAAM,eAAE;aAC3B,MAAM,CAAC,EAAE,EAAE,EAAE,cAAK,CAAC,EAAE,EAAE,CAAC;aACxB,IAAI,CAAC,cAAK,CAAC;aACX,KAAK,CAAC,IAAA,gBAAE,EAAC,cAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;aACnC,KAAK,CAAC,CAAC,CAAC,CAAC;QAEZ,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAA,0BAAW,EAAC,oDAAoD,EAAE,GAAG,CAAC,CAAC;QAC/E,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,eAAE;aACpB,MAAM,CAAC,qBAAY,CAAC;aACpB,KAAK,CAAC,IAAA,gBAAE,EAAC,qBAAY,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;aACpC,SAAS,CAAC,EAAE,EAAE,EAAE,qBAAY,CAAC,EAAE,EAAE,CAAC,CAAC;QAEtC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,IAAA,0BAAW,EAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,OAAe,EAAE,SAAmB;QAEvD,MAAM,eAAe,GAAG,MAAM,eAAE;aAC7B,MAAM,CAAC,EAAE,EAAE,EAAE,qBAAY,CAAC,EAAE,EAAE,CAAC;aAC/B,IAAI,CAAC,qBAAY,CAAC;aAClB,KAAK,CAAC,IAAA,gBAAE,EAAC,qBAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;QAE5C,MAAM,iBAAiB,GAAG,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC7D,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;QAE/E,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAA,0BAAW,EAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,SAAS,CAAC,MAAM,KAAK,iBAAiB,CAAC,MAAM,EAAE,CAAC;YAClD,MAAM,IAAA,0BAAW,EAAC,yCAAyC,EAAE,GAAG,CAAC,CAAC;QACpE,CAAC;QAGD,MAAM,cAAc,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,CACvD,eAAE;aACC,MAAM,CAAC,qBAAY,CAAC;aACpB,GAAG,CAAC;YACH,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;aACD,KAAK,CAAC,IAAA,gBAAE,EAAC,qBAAY,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,CACxC,CAAC;QAEF,MAAM,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAGlC,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,OAAe;QAEjC,MAAM,WAAW,GAAG,MAAM,eAAE;aACzB,MAAM,CAAC,EAAE,KAAK,EAAE,qBAAY,CAAC,EAAE,EAAE,CAAC;aAClC,IAAI,CAAC,qBAAY,CAAC;aAClB,KAAK,CAAC,IAAA,gBAAE,EAAC,qBAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;QAG5C,MAAM,SAAS,GAAG,MAAM,eAAE;aACvB,MAAM,CAAC,EAAE,KAAK,EAAE,cAAK,CAAC,EAAE,EAAE,CAAC;aAC3B,IAAI,CAAC,cAAK,CAAC;aACX,KAAK,CAAC,IAAA,iBAAG,EACR,IAAA,gBAAE,EAAC,cAAK,CAAC,OAAO,EAAE,OAAO,CAAC,EAC1B,IAAA,gBAAE,EAAC,cAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAC5B,CAAC,CAAC;QAIL,OAAO;YACL,OAAO,EAAE,WAAW,CAAC,MAAM;YAC3B,KAAK,EAAE,SAAS,CAAC,MAAM;SAExB,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,OAAe,EAAE,MAAc;QACtD,MAAM,KAAK,GAAG,MAAM,eAAE;aACnB,MAAM,CAAC;YACN,cAAc,EAAE,eAAM,CAAC,cAAc;YACrC,QAAQ,EAAE,eAAM,CAAC,QAAQ;SAC1B,CAAC;aACD,IAAI,CAAC,eAAM,CAAC;aACZ,KAAK,CAAC,IAAA,gBAAE,EAAC,eAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;aAC7B,KAAK,CAAC,CAAC,CAAC,CAAC;QAEZ,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAClB,OAAO,KAAK,CAAC;QACf,CAAC;QAKD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAzVD,oCAyVC;AAEY,QAAA,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC"}