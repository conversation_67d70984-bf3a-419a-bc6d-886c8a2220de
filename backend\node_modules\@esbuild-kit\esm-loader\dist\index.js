import l from"path";import{fileURLToPath as y,pathToFileURL as U}from"url";import{installSourceMapSupport as I,compareNodeVersion as m,resolveTsPath as M,transform as S,transformDynamicImport as k}from"@esbuild-kit/core-utils";import{parseTsconfig as A,getTsconfig as J,createFilesMatcher as L,createPathsMatcher as W}from"get-tsconfig";import R from"fs";const f=new Map;async function b(t){if(f.has(t))return f.get(t);if(!await R.promises.access(t).then(()=>!0,()=>!1)){f.set(t,void 0);return}const e=await R.promises.readFile(t,"utf8");try{const n=JSON.parse(e);return f.set(t,n),n}catch{throw new Error(`Error parsing: ${t}`)}}async function $(t){let s=new URL("package.json",t);for(;!s.pathname.endsWith("/node_modules/package.json");){const e=y(s),n=await b(e);if(n)return n;const r=s;if(s=new URL("../package.json",s),s.pathname===r.pathname)break}}async function x(t){var s;const e=await $(t);return(s=e==null?void 0:e.type)!=null?s:"commonjs"}const u=I(),d=process.env.ESBK_TSCONFIG_PATH?{path:l.resolve(process.env.ESBK_TSCONFIG_PATH),config:A(process.env.ESBK_TSCONFIG_PATH)}:J(),N=d&&L(d),O=d&&W(d),w="file://",g=/\.([cm]?ts|[tj]sx)($|\?)/,_=/\.json(?:$|\?)/,C=t=>{const s=l.extname(t);if(s===".json")return"json";if(s===".mjs"||s===".mts")return"module";if(s===".cjs"||s===".cts")return"commonjs"},j=t=>{const s=C(t);if(s)return s;if(g.test(t))return x(t)},v=/\/(?:$|\?)/,K=m([20,0,0])>=0;let P=process.send?process.send.bind(process):void 0,E;const q=({port:t})=>(E=t,P=t.postMessage.bind(t),`
	const require = getBuiltin('module').createRequire("${import.meta.url}");
	require('@esbuild-kit/core-utils').installSourceMapSupport(port);
	if (process.send) {
		port.addListener('message', (message) => {
			if (message.type === 'dependency') {
				process.send(message);
			}
		});
	}
	port.unref(); // Allows process to exit without waiting for port to close
	`),B=K?q:void 0,G=[".js",".json",".ts",".tsx",".jsx"];async function T(t,s,e){const[n,r]=t.split("?");let i;for(const a of G)try{return await h(n+a+(r?`?${r}`:""),s,e,!0)}catch(o){if(i===void 0&&o instanceof Error){const{message:c}=o;o.message=o.message.replace(`${a}'`,"'"),o.stack=o.stack.replace(c,o.message),i=o}}throw i}async function F(t,s,e){const n=v.test(t),r=n?"index":"/index",[i,a]=t.split("?");try{return await T(i+r+(a?`?${a}`:""),s,e)}catch(o){if(!n)try{return await T(t,s,e)}catch{}const c=o,{message:p}=c;throw c.message=c.message.replace(`${r.replace("/",l.sep)}'`,"'"),c.stack=c.stack.replace(p,c.message),c}}const H=/^\.{1,2}\//,Q=m([14,13,1])>=0||m([12,20,0])>=0,h=async function(t,s,e,n){var r;if(!Q&&t.startsWith("node:")&&(t=t.slice(5)),v.test(t))return await F(t,s,e);const i=t.startsWith(w)||H.test(t);if(O&&!i&&!((r=s.parentURL)!=null&&r.includes("/node_modules/"))){const o=O(t);for(const c of o)try{return await h(U(c).toString(),s,e)}catch{}}if(g.test(s.parentURL)){const o=M(t);if(o)try{return await h(o,s,e,!0)}catch(c){const{code:p}=c;if(p!=="ERR_MODULE_NOT_FOUND"&&p!=="ERR_PACKAGE_PATH_NOT_EXPORTED")throw c}}let a;try{a=await e(t,s)}catch(o){if(o instanceof Error&&!n){const{code:c}=o;if(c==="ERR_UNSUPPORTED_DIR_IMPORT")try{return await F(t,s,e)}catch(p){if(p.code!=="ERR_PACKAGE_IMPORT_NOT_DEFINED")throw p}if(c==="ERR_MODULE_NOT_FOUND")try{return await T(t,s,e)}catch{}}throw o}return!a.format&&a.url.startsWith(w)&&(a.format=await j(a.url)),a},X=async function(t,s,e){var n;P&&P({type:"dependency",path:t}),_.test(t)&&(s.importAssertions||(s.importAssertions={}),s.importAssertions.type="json");const r=await e(t,s);if(!r.source)return r;const i=t.startsWith("file://")?y(t):t,a=r.source.toString();if(r.format==="json"||g.test(t)){const o=await S(a,i,{tsconfigRaw:(n=N)==null?void 0:n(i)});return{format:"module",source:u(o,t,E)}}if(r.format==="module"){const o=k(i,a);o&&(r.source=u(o,t,E))}return r},V=async function(t,s,e){if(_.test(t))return{format:"module"};try{return await e(t,s,e)}catch(n){if(n.code==="ERR_UNKNOWN_FILE_EXTENSION"&&t.startsWith(w)){const r=await j(t);if(r)return{format:r}}throw n}},z=async function(t,s,e){var n;const{url:r}=s,i=r.startsWith("file://")?y(r):r;if(process.send&&process.send({type:"dependency",path:r}),_.test(r)||g.test(r)){const o=await S(t.toString(),i,{tsconfigRaw:(n=N)==null?void 0:n(i)});return{source:u(o,r)}}const a=await e(t,s,e);if(s.format==="module"){const o=k(i,a.source.toString());o&&(a.source=u(o,r))}return a},D=m([16,12,0])<0,Y=D?V:void 0,Z=D?z:void 0;export{Y as getFormat,B as globalPreload,X as load,h as resolve,Z as transformSource};
