{"version": 3, "file": "rbacService.js", "sourceRoot": "", "sources": ["../../src/services/rbacService.ts"], "names": [], "mappings": ";;;AAAA,iDAAsC;AACtC,yCAAyE;AACzE,6CAAsC;AACtC,0EAA2G;AAC3G,6DAAyD;AAEzD,MAAa,WAAW;IAItB,KAAK,CAAC,aAAa,CACjB,MAAc,EACd,cAAsB,EACtB,UAAsB;QAEtB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;YACxE,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAClD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,YAAY,GAAG,iCAAW,CAAC,UAAU,CAAC,CAAC;YAC7C,OAAO,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACvE,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAClB,MAAc,EACd,cAAsB,EACtB,WAAiB;QAEjB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;YACxE,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAClD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,aAAa,GAAG,oCAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,iBAAiB,GAAG,oCAAc,CAAC,WAAW,CAAC,CAAC;YAEtD,OAAO,aAAa,IAAI,iBAAiB,CAAC;QAC5C,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,cAAsB;QAC5D,MAAM,UAAU,GAAG,MAAM,eAAE;aACxB,MAAM,CAAC;YACN,EAAE,EAAE,4BAAmB,CAAC,EAAE;YAC1B,IAAI,EAAE,4BAAmB,CAAC,IAAI;YAC9B,MAAM,EAAE,4BAAmB,CAAC,MAAM;YAClC,QAAQ,EAAE,4BAAmB,CAAC,QAAQ;SACvC,CAAC;aACD,IAAI,CAAC,4BAAmB,CAAC;aACzB,KAAK,CACJ,IAAA,iBAAG,EACD,IAAA,gBAAE,EAAC,4BAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,EACtC,IAAA,gBAAE,EAAC,4BAAmB,CAAC,cAAc,EAAE,cAAc,CAAC,CACvD,CACF;aACA,KAAK,CAAC,CAAC,CAAC,CAAC;QAEZ,OAAO,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACtD,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,MAAc;QACrC,MAAM,WAAW,GAAG,MAAM,eAAE;aACzB,MAAM,CAAC;YACN,YAAY,EAAE;gBACZ,EAAE,EAAE,sBAAa,CAAC,EAAE;gBACpB,IAAI,EAAE,sBAAa,CAAC,IAAI;gBACxB,IAAI,EAAE,sBAAa,CAAC,IAAI;gBACxB,MAAM,EAAE,sBAAa,CAAC,MAAM;gBAC5B,QAAQ,EAAE,sBAAa,CAAC,QAAQ;aACjC;YACD,UAAU,EAAE;gBACV,EAAE,EAAE,4BAAmB,CAAC,EAAE;gBAC1B,IAAI,EAAE,4BAAmB,CAAC,IAAI;gBAC9B,MAAM,EAAE,4BAAmB,CAAC,MAAM;gBAClC,QAAQ,EAAE,4BAAmB,CAAC,QAAQ;aACvC;SACF,CAAC;aACD,IAAI,CAAC,4BAAmB,CAAC;aACzB,SAAS,CAAC,sBAAa,EAAE,IAAA,gBAAE,EAAC,sBAAa,CAAC,EAAE,EAAE,4BAAmB,CAAC,cAAc,CAAC,CAAC;aAClF,KAAK,CAAC,IAAA,gBAAE,EAAC,4BAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;aAC7C,OAAO,CAAC,4BAAmB,CAAC,QAAQ,CAAC,CAAC;QAEzC,OAAO,WAAW,CAAC;IACrB,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAAC,cAAsB,EAAE,gBAAwB;QAE3E,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;QAC7F,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAA,0BAAW,EAAC,uDAAuD,EAAE,GAAG,CAAC,CAAC;QAClF,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,eAAE;aACrB,MAAM,CAAC;YACN,IAAI,EAAE;gBACJ,EAAE,EAAE,cAAK,CAAC,EAAE;gBACZ,KAAK,EAAE,cAAK,CAAC,KAAK;gBAClB,SAAS,EAAE,cAAK,CAAC,SAAS;gBAC1B,QAAQ,EAAE,cAAK,CAAC,QAAQ;gBACxB,MAAM,EAAE,cAAK,CAAC,MAAM;gBACpB,MAAM,EAAE,cAAK,CAAC,MAAM;aACrB;YACD,UAAU,EAAE;gBACV,EAAE,EAAE,4BAAmB,CAAC,EAAE;gBAC1B,IAAI,EAAE,4BAAmB,CAAC,IAAI;gBAC9B,MAAM,EAAE,4BAAmB,CAAC,MAAM;gBAClC,QAAQ,EAAE,4BAAmB,CAAC,QAAQ;aACvC;SACF,CAAC;aACD,IAAI,CAAC,4BAAmB,CAAC;aACzB,SAAS,CAAC,cAAK,EAAE,IAAA,gBAAE,EAAC,cAAK,CAAC,EAAE,EAAE,4BAAmB,CAAC,MAAM,CAAC,CAAC;aAC1D,KAAK,CAAC,IAAA,gBAAE,EAAC,4BAAmB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;aAC7D,OAAO,CAAC,4BAAmB,CAAC,QAAQ,CAAC,CAAC;QAEzC,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,gBAAgB,CACpB,cAAsB,EACtB,YAAoB,EACpB,OAAa,EACb,gBAAwB;QAGxB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,cAAc,EAAE,oBAAoB,CAAC,CAAC;QACvG,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAA,0BAAW,EAAC,yDAAyD,EAAE,GAAG,CAAC,CAAC;QACpF,CAAC;QAGD,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;QAC5F,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1B,MAAM,IAAA,0BAAW,EAAC,sDAAsD,EAAE,GAAG,CAAC,CAAC;QACjF,CAAC;QAGD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QACpF,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAA,0BAAW,EAAC,kDAAkD,EAAE,GAAG,CAAC,CAAC;QAC7E,CAAC;QAGD,MAAM,mBAAmB,GAAG,oCAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QACtE,MAAM,sBAAsB,GAAG,oCAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACrE,MAAM,YAAY,GAAG,oCAAc,CAAC,OAAO,CAAC,CAAC;QAG7C,IAAI,sBAAsB,IAAI,mBAAmB,EAAE,CAAC;YAClD,MAAM,IAAA,0BAAW,EAAC,4DAA4D,EAAE,GAAG,CAAC,CAAC;QACvF,CAAC;QAGD,IAAI,YAAY,IAAI,mBAAmB,IAAI,oBAAoB,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACjF,MAAM,IAAA,0BAAW,EAAC,kDAAkD,EAAE,GAAG,CAAC,CAAC;QAC7E,CAAC;QAGD,IAAI,gBAAgB,CAAC,IAAI,KAAK,OAAO,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;YAC7D,MAAM,UAAU,GAAG,MAAM,eAAE;iBACxB,MAAM,CAAC,EAAE,KAAK,EAAE,4BAAmB,CAAC,EAAE,EAAE,CAAC;iBACzC,IAAI,CAAC,4BAAmB,CAAC;iBACzB,KAAK,CACJ,IAAA,iBAAG,EACD,IAAA,gBAAE,EAAC,4BAAmB,CAAC,cAAc,EAAE,cAAc,CAAC,EACtD,IAAA,gBAAE,EAAC,4BAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,EACrC,IAAA,gBAAE,EAAC,4BAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CACzC,CACF,CAAC;YAEJ,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBAC3B,MAAM,IAAA,0BAAW,EAAC,kDAAkD,EAAE,GAAG,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;QAGD,MAAM,iBAAiB,GAAG,MAAM,eAAE;aAC/B,MAAM,CAAC,4BAAmB,CAAC;aAC3B,GAAG,CAAC;YACH,IAAI,EAAE,OAAO;YACb,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;aACD,KAAK,CAAC,IAAA,gBAAE,EAAC,4BAAmB,CAAC,EAAE,EAAE,gBAAgB,CAAC,EAAE,CAAC,CAAC;aACtD,SAAS,EAAE,CAAC;QAEf,OAAO,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IAKD,KAAK,CAAC,YAAY,CAChB,cAAsB,EACtB,YAAoB,EACpB,gBAAwB;QAGxB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,cAAc,EAAE,oBAAoB,CAAC,CAAC;QACvG,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAA,0BAAW,EAAC,yDAAyD,EAAE,GAAG,CAAC,CAAC;QACpF,CAAC;QAGD,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;QAC5F,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QAEpF,IAAI,CAAC,oBAAoB,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC/C,MAAM,IAAA,0BAAW,EAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;QAC/C,CAAC;QAGD,MAAM,mBAAmB,GAAG,oCAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QACtE,MAAM,eAAe,GAAG,oCAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAG9D,IAAI,eAAe,IAAI,mBAAmB,EAAE,CAAC;YAC3C,MAAM,IAAA,0BAAW,EAAC,oDAAoD,EAAE,GAAG,CAAC,CAAC;QAC/E,CAAC;QAGD,IAAI,gBAAgB,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACtC,MAAM,UAAU,GAAG,MAAM,eAAE;iBACxB,MAAM,CAAC,EAAE,KAAK,EAAE,4BAAmB,CAAC,EAAE,EAAE,CAAC;iBACzC,IAAI,CAAC,4BAAmB,CAAC;iBACzB,KAAK,CACJ,IAAA,iBAAG,EACD,IAAA,gBAAE,EAAC,4BAAmB,CAAC,cAAc,EAAE,cAAc,CAAC,EACtD,IAAA,gBAAE,EAAC,4BAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,EACrC,IAAA,gBAAE,EAAC,4BAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CACzC,CACF,CAAC;YAEJ,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBAC3B,MAAM,IAAA,0BAAW,EAAC,kDAAkD,EAAE,GAAG,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;QAGD,MAAM,eAAE;aACL,MAAM,CAAC,4BAAmB,CAAC;aAC3B,KAAK,CAAC,IAAA,gBAAE,EAAC,4BAAmB,CAAC,EAAE,EAAE,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,cAAsB;QAC7D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QACxE,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAClD,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,eAAe,GAAiB,EAAE,CAAC;QAEzC,KAAK,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,iCAAW,CAAC,EAAE,CAAC;YACrE,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3C,eAAe,CAAC,IAAI,CAAC,UAAwB,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;CACF;AAxRD,kCAwRC;AAEY,QAAA,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC"}